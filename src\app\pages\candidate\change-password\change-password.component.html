<!-- Header avec navigation -->
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover">
          Accueil
        </a>

        <!-- Bouton retour dashboard -->
        <a mat-button routerLink="/candidate/dashboard" class="nav-item me-3 underline-hover">
          Dashboard
        </a>

        <!-- Bouton profil -->
        <div class="profile-menu-container">
          <button mat-icon-button
                 [matMenuTriggerFor]="profileMenu"
                 aria-label="Profil utilisateur"
                 class="profile-button">
            <div class="profile-avatar">
              <mat-icon>person</mat-icon>
            </div>
          </button>

          <mat-menu #profileMenu="matMenu" xPosition="before" class="profile-dropdown">
            <button mat-menu-item routerLink="/candidate/profile">
              <mat-icon>account_circle</mat-icon>
              <span>Profil</span>
            </button>
            <button mat-menu-item routerLink="/candidate/cv">
              <mat-icon>description</mat-icon>
              <span>Mon CV</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item routerLink="/login">
              <mat-icon>exit_to_app</mat-icon>
              <span>Se déconnecter</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>

<!-- Contenu principal -->
<div class="change-password-container">
  <div class="form-wrapper">
    <div class="form-header">
      <mat-icon class="header-icon">lock</mat-icon>
      <h1>Changer le mot de passe</h1><br>
      <p>Modifiez votre mot de passe pour sécuriser votre compte</p>
    </div>

    <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()" class="password-form">

      <!-- Mot de passe actuel -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Mot de passe actuel</mat-label>
        <input matInput
               [type]="hideCurrentPassword ? 'password' : 'text'"
               formControlName="currentPassword"
               placeholder="Entrez votre mot de passe actuel">
        <button mat-icon-button
                matSuffix
                type="button"
                (click)="hideCurrentPassword = !hideCurrentPassword"
                [attr.aria-label]="'Afficher le mot de passe'"
                [attr.aria-pressed]="!hideCurrentPassword">
          <mat-icon>{{hideCurrentPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="hasError('currentPassword')">
          {{ getErrorMessage('currentPassword') }}
        </mat-error>
      </mat-form-field>

      <!-- Nouveau mot de passe -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Nouveau mot de passe</mat-label>
        <input matInput
               [type]="hideNewPassword ? 'password' : 'text'"
               formControlName="newPassword"
               placeholder="Entrez votre nouveau mot de passe">
        <button mat-icon-button
                matSuffix
                type="button"
                (click)="hideNewPassword = !hideNewPassword"
                [attr.aria-label]="'Afficher le mot de passe'"
                [attr.aria-pressed]="!hideNewPassword">
          <mat-icon>{{hideNewPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="hasError('newPassword') && !changePasswordForm.get('newPassword')?.hasError('passwordStrength')">
          {{ getErrorMessage('newPassword') }}
        </mat-error>
        <mat-error *ngIf="hasError('newPassword') && changePasswordForm.get('newPassword')?.hasError('passwordStrength')">
          Le mot de passe doit contenir au moins 8 caractères avec majuscule, minuscule, chiffre et caractère spécial
        </mat-error>
      </mat-form-field>

      <!-- Confirmation du nouveau mot de passe -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Confirmer le nouveau mot de passe</mat-label>
        <input matInput
               [type]="hideConfirmPassword ? 'password' : 'text'"
               formControlName="confirmPassword"
               placeholder="Confirmez votre nouveau mot de passe">
        <button mat-icon-button
                matSuffix
                type="button"
                (click)="hideConfirmPassword = !hideConfirmPassword"
                [attr.aria-label]="'Afficher le mot de passe'"
                [attr.aria-pressed]="!hideConfirmPassword">
          <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="hasError('confirmPassword')">
          {{ getErrorMessage('confirmPassword') }}
        </mat-error>
      </mat-form-field>

      <!-- Boutons d'action -->
      <div class="form-actions">
        <button mat-button
                type="button"
                class="cancel-btn"
                (click)="onCancel()"
                [disabled]="isLoading">
          Annuler
        </button>

        <button mat-raised-button
                type="submit"
                color="primary"
                class="submit-btn"
                [disabled]="changePasswordForm.invalid || isLoading">
          <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
          <span *ngIf="!isLoading">Modifier</span>
          <span *ngIf="isLoading">Modification en cours...</span>
        </button>
      </div>
    </form>

    <!-- Conseils de sécurité -->
    <div class="security-tips">
      <h3><mat-icon>security</mat-icon> Conseils de sécurité</h3>
      <ul>
        <li>Utilisez un mot de passe unique pour ce compte</li>
        <li>Évitez d'utiliser des informations personnelles</li>
        <li>Changez votre mot de passe régulièrement</li>
        <li>Ne partagez jamais votre mot de passe</li>
      </ul>
    </div>
  </div>
</div>

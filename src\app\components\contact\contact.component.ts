import { Component , OnInit  } from '@angular/core';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss']
})
export class ContactComponent implements OnInit {
      constructor(private titleService: Title){}
    ngOnInit() {
      this.titleService.setTitle('Notre Contact');
    }

    contactMethods = [
    {
      title: 'VISITEZ RecrutIQ',
      icon: 'fas fa-home',
      description: 'Rendez-vous dans nos bureaux RecrutIQ pour découvrir notre plateforme IA de recrutement et rencontrer notre équipe d\'experts.',
      details: 'Campus Technologique RecrutIQ, Rabat, Maroc',
      link: null,
      action: null
    },
    {
      title: 'APPELEZ RecrutIQ',
      icon: 'fas fa-phone',
      description: 'Contactez directement notre équipe support RecrutIQ pour toute question sur notre solution de recrutement intelligente.',
      details: '+212638792560',
      link: 'tel:+212638792560',
      action: 'call'
    },
    {
      title: 'EMAIL RecrutIQ',
      icon: 'fas fa-envelope',
      description: 'Écrivez-nous pour en savoir plus sur RecrutIQ, demander une démonstration ou obtenir un devis personnalisé.',
      details: '<EMAIL>',
      link: 'mailto:<EMAIL>',
      action: 'email'
    }
  ];

  // Méthode pour gérer les clics sur les cartes de contact
  handleContactClick(method: any): void {
    if (method.link) {
      window.open(method.link, '_self');
    }
  }

}

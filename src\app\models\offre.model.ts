export interface OffreRequest {
  titre: string;
  description: string;
  domaine: string;
  ville: string;
  typeContrat: string;
  competences: string[];
  dateLimite: string;
  salaire?: string;
  heuresParSemaine?: number;
}

export interface OffreResponse {
  id: number;
  titre: string;
  description: string;
  domaine: string;
  ville: string;
  typeContrat: string;
  competences: string[];
  dateLimite: string;
  salaire?: string;
  heuresParSemaine?: number;
  recruteurId: number;
  dateCreation: string;
  dateModification: string;
  statut: string;
}

export enum TypeContrat {
  CDI = 'CDI',
  CDD = 'CDD',
  FREELANCE = 'FREELANCE',
  STAGE = 'STAGE',
  ALTERNANCE = 'ALTERNANCE'
}

export enum StatutOffre {
  ACTIVE = 'ACTIVE',
  CLOTUREE = 'CLOTUREE',
  SUPPRIMEE = 'SUPPRIMEE'
} 
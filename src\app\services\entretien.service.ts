import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { firstValueFrom } from 'rxjs';

export interface Entretien {
  id: number;
  postulationId: number;
  candidatId: number;
  recruteurId: number;
  dateEntretien: string;
  duree: number;
  statut: 'PLANIFIE' | 'EN_COURS' | 'TERMINE' | 'ANNULE';
  score: number;
  feedback: string;
  questions: Question[];
  reponses: Reponse[];
}

export interface Question {
  id: number;
  entretienId: number;
  texte: string;
  type: 'TECHNIQUE' | 'COMPORTEMENTAL' | 'CULTURE';
  difficulte: 'FACILE' | 'MOYEN' | 'DIFFICILE';
  tempsReponse: number;
}

export interface Reponse {
  id: number;
  questionId: number;
  entretienId: number;
  texte: string;
  score: number;
  feedback: string;
  dateReponse: string;
}

export interface EntretienRequest {
  postulationId: number;
  dateEntretien: string;
  duree: number;
}

export interface InterviewResponse {
  id: number;
  offreId: number;
  candidatId: number;
  score: number;
  feedback: string;
  dateEntretien: Date;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
}

@Injectable({
  providedIn: 'root'
})
export class EntretienService {
  submitAnswer(token: string, questionIndex: number, transcript: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/interview/${token}/answer`, {
      questionIndex,
      reponse: transcript
    });
  }
  getInterviewDetails(token: string): Observable<{ questions: string[] }> {
    return this.http.get<{ questions: string[] }>(`${this.apiUrl}/interview/${token}`);
  }
  private apiUrl = `${environment.apiUrl}/api/entretiens`;

  constructor(private http: HttpClient) {}

  // Gestion des entretiens
  getEntretienById(id: number): Observable<Entretien> {
    return this.http.get<Entretien>(`${this.apiUrl}/${id}`);
  }

  getEntretiensCandidat(): Observable<Entretien[]> {
    return this.http.get<Entretien[]>(`${this.apiUrl}/candidat`);
  }

  getEntretiensRecruteur(): Observable<Entretien[]> {
    return this.http.get<Entretien[]>(`${this.apiUrl}/recruteur`);
  }

  planifierEntretien(request: EntretienRequest): Observable<Entretien> {
    return this.http.post<Entretien>(`${this.apiUrl}/planifier`, request);
  }

  annulerEntretien(id: number, motif: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/annuler`, { motif });
  }

  // Gestion des questions et réponses
  getQuestionsEntretien(entretienId: number): Observable<Question[]> {
    return this.http.get<Question[]>(`${this.apiUrl}/${entretienId}/questions`);
  }

  soumettreReponse(entretienId: number, questionId: number, reponse: string): Observable<Reponse> {
    return this.http.post<Reponse>(`${this.apiUrl}/${entretienId}/questions/${questionId}/reponse`, {
      texte: reponse
    });
  }

  // Évaluation et feedback
  evaluerEntretien(entretienId: number, score: number, feedback: string): Observable<Entretien> {
    return this.http.post<Entretien>(`${this.apiUrl}/${entretienId}/evaluation`, {
      score,
      feedback
    });
  }

  evaluerReponse(entretienId: number, questionId: number, score: number, feedback: string): Observable<Reponse> {
    return this.http.post<Reponse>(`${this.apiUrl}/${entretienId}/questions/${questionId}/evaluation`, {
      score,
      feedback
    });
  }

  // Statistiques
  getStatistiquesEntretiens(): Observable<any> {
    return this.http.get(`${this.apiUrl}/statistiques`);
  }

  getStatistiquesQuestions(): Observable<any> {
    return this.http.get(`${this.apiUrl}/questions/statistiques`);
  }

  // Rappels et notifications
  envoyerRappelEntretien(entretienId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${entretienId}/rappel`, {});
  }

  confirmerPresence(entretienId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${entretienId}/confirmation`, {});
  }

  saveInterview(offreId: number): Observable<InterviewResponse> {
    return this.http.post<InterviewResponse>(`${this.apiUrl}/save`, { offreId });
  }

  getInterview(interviewId: number): Observable<InterviewResponse> {
    return this.http.get<InterviewResponse>(`${this.apiUrl}/${interviewId}`);
  }

  getCandidateInterviews(candidatId: number): Observable<InterviewResponse[]> {
    return this.http.get<InterviewResponse[]>(`${this.apiUrl}/candidat/${candidatId}`);
  }

  getOffreInterviews(offreId: number): Observable<InterviewResponse[]> {
    return this.http.get<InterviewResponse[]>(`${this.apiUrl}/offre/${offreId}`);
  }
} 
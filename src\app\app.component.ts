import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'pfe_interface';
  isLoading: boolean = true;
  private routerSubscription: Subscription = new Subscription();

  constructor(private router: Router) {}

  ngOnInit() {
    setTimeout(() => {
      this.isLoading = false; // simule un chargement
    }, 2000);

    // Écouter les changements de route pour mettre à jour le background
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateBackgroundImage();
      });

    // Mettre à jour le background initial
    this.updateBackgroundImage();
  }

  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  isHomePage(): boolean {
    const currentRoute = this.router.url;
    return currentRoute === '/' || currentRoute === '';
  }

  // Fonction pour déterminer si le footer doit être affiché
  shouldShowFooter(): boolean {
    const currentRoute = this.router.url;
    const footerPages = [
      '/',
      '',
      '/reset-password',
      '/forgot-password',
      '/a-propos',
      '/contact',
      '/login',
      '/register'
    ];
    return footerPages.includes(currentRoute);
  }

  // Fonction pour mettre à jour l'image de background selon la page
  private updateBackgroundImage(): void {
    const body = document.body;

    if (this.isHomePage()) {
      // Page d'accueil : utiliser bg.png sans overlay
      body.style.backgroundImage = "url('/assets/images/bg.png')";
      body.classList.add('home-page-body');
      body.classList.remove('other-page-body');
    } else {
      // Toutes les autres pages : utiliser backgroundpic.png avec overlay assombri
      body.style.backgroundImage = "url('/assets/images/backgroundpic.png')";
      body.classList.remove('home-page-body');
      body.classList.add('other-page-body');
    }
  }
}

/* job-form.component.scss */
:host {
    display: block;
    width: 100%;
  }
  
  .form-container {
    background-color: #5a0045;
    border-radius: 10px;
    padding: 30px;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    color: white;
  }
  
  h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 36px;
    color : violet;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  label {
    display: block;
    margin-bottom: 10px;
    font-size: 18px;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 5px;
    background-color: #3a002d;
    color: white;
    font-size: 16px;
    box-sizing: border-box;
  }
  
  select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px;
  }
  
  .error {
    color: #ff6b6b;
    font-size: 14px;
    margin-top: 5px;
  }
  
  .form-actions {
    margin-top: 30px;
    text-align: center;
  }
  
  .submit-button {
    background-color: #9c27b0;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 30px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s;
    
  &:hover {
    background-color: #ff4081;
    color: #ffffff;
    box-shadow: 0 6px 14px rgba(255, 64, 129, 0.5);
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 64, 129, 0.4);
  }
  }
  
  /* Adaptation pour affichage responsive */
  @media (max-width: 768px) {
    .form-container {
      padding: 20px;
    }
    
    h1 {
      font-size: 28px;
      color: lightcoral;
    }
  }

// -----------------------------------------------------------------------------

:host {
  /* Cibler uniquement le composant header */
  header {
    background-color: transparent;
    padding-bottom: 0;
    margin-bottom: 0;
    border: none;
    box-shadow: none;
  }

  .navbar {
    padding: 0rem 1rem;
    border: none;
  }

  .logo-container {
    display: flex;
    align-items: center;
    border: none;
  }

  .navbar-brand, 
  .nav-item, 
  .profile-button {
    border: none;
  }

  .logo-image {
    max-height: 100px;
    max-width: auto;
  }

  .nav-item {
    color: white;
    font-weight: 500;
    &:hover {
      color: violet;
    }
  }

  .profile-menu-container {
    position: relative;
    border: none;
  }

  .profile-button {
    padding: 0;
    background: none;
    border: none;
  }

  .profile-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: #3D0B37;
    background-color: white;
    border-radius: 50%;
    overflow: hidden;
    border: none;
  }

  /* Surcharger les styles Angular Material spécifiquement pour ce composant */
  ::ng-deep .mat-button,
  ::ng-deep .mat-icon-button {
    border: none;
    box-shadow: none;
  }
}

.underline-hover::after {
content: "";
position: absolute;
top: 2.5rem;
bottom: 0;
left: 0;
width: 0%;
height: 2px;
color: #9c27b0;
background-color: white;
transition: width 0.3s ease;
}

.underline-hover:hover::after {
width: 100%;
}

// ! --------------------------------------------------

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
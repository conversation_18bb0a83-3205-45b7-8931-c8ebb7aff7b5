import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DashboardComponent } from './dashboard/dashboard.component';
import { PostJobComponent } from './post-job/post-job.component';
import { InterviewResultsComponent } from './interview-results/interview-results.component';
import { ChangePasswordRecruteurComponent } from './change-password-recruteur/change-password-recruteur.component';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  { path: 'dashboard', component: DashboardComponent },
  { path: 'post-job', component: PostJobComponent },
  { path: 'interview-results', component: InterviewResultsComponent },
  { path: 'change-password-recruteur', component: ChangePasswordRecruteurComponent } 
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RecruteurRoutingModule { }

// .about-container {
//   max-width: 800px;
//   margin: auto;
//   padding: 2rem;
//   background-color: transparent;
//   border-radius: 12px;
//   box-shadow: 0 8px 16px rgba(255, 255, 255, 0.05);
//   backdrop-filter: blur(10px);
//   background-color: rgba(255, 255, 255, 0.05);

//   .ng{
//     // text-align: center;
//     color: lightcoral;
//     margin-bottom: 1rem;
//   }

//   h1 {
//     text-align: center;
//     color: lightcoral;
//     margin-bottom: 1rem;
//   }

//   .intro {
//     text-align: center;
//     font-size: 1.1rem;
//     color: #cccccc;
//     margin-bottom: 2rem;
//   }

//   .section {
//     margin-bottom: 2rem;

//     h2 {
//       color: lightgreen; // Violet clair adapté aux fonds sombres
//       margin-bottom: 0.5rem;
//     }

//     p,
//     ul {
//       color: #dddddd;
//       line-height: 1.6;
//     }

//     ul {
//       padding-left: 1.5rem;

//       li {
//         margin-bottom: 0.5rem;
//         list-style-type: disc;
//       }
//     }
//   }
// }

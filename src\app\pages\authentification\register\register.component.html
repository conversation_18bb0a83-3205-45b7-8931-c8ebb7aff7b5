<!-- <div class="stepper-container">
  <div class="logo-container">
    <img src="assets/images/loogo.png" alt="Logo" class="header-logo">
</div> -->
<!-- <div class="logo-stepper-container">
  <img src="assets/images/loogo.png" alt="Logo" class="logo-stepper-img" />
</div> -->
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>
<div class="stepper-container">
<mat-horizontal-stepper linear #stepper>
  <mat-step [stepControl]="firstFormGroup" class="">
    <form [formGroup]="firstFormGroup">
  
      <ng-template matStepLabel>Informations Personnels</ng-template>
      <mat-form-field appearance="fill" >
        <mat-label>Nom</mat-label>
        <input matInput placeholder="Entrez votre nom" formControlName="nom" required />
        <mat-error *ngIf="firstFormGroup.get('nom')?.hasError('required')">
          Le nom est obligatoire
        </mat-error>
        <mat-error *ngIf="firstFormGroup.get('nom')?.hasError('pattern')">
          Le nom ne doit contenir que des lettres
        </mat-error>
      </mat-form-field><br>

      <mat-form-field appearance="fill">
        <mat-label>Prénom</mat-label>
        <input matInput placeholder="Entrez votre prénom" formControlName="prenom" required />
        <mat-error *ngIf="firstFormGroup.get('prenom')?.hasError('required')">
          Le prénom est obligatoire
        </mat-error>      
        <mat-error *ngIf="firstFormGroup.get('prenom')?.hasError('pattern')">
          Le prénom ne doit contenir que des lettres
        </mat-error>
      </mat-form-field><br>

      <mat-form-field appearance="fill">
        <mat-label>Email</mat-label>
        <input
          matInput
          formControlName="email"
          placeholder="ex: <EMAIL>"
          required
        />
        <mat-error *ngIf="firstFormGroup.get('email')?.hasError('required')">
          L'email est requis
        </mat-error>
        <mat-error *ngIf="firstFormGroup.get('email')?.hasError('pattern')">
          Format d'email invalide
        </mat-error>
      </mat-form-field><br>

      <mat-form-field appearance="fill">
        <mat-label>Numéro de téléphone</mat-label>
        <input
          matInput
          formControlName="telephone"
          placeholder="063872560"
          type="tel"
        />
        <mat-error *ngIf="firstFormGroup.get('telephone')?.hasError('required')">
          Le numéro est requis
        </mat-error>
        <mat-error *ngIf="firstFormGroup.get('telephone')?.hasError('pattern')">
          Numéro invalide
        </mat-error>
      </mat-form-field>

      <div>
        <button mat-button matStepperNext [disabled]="firstFormGroup.invalid" class="btn btn-next">Suivant</button>
      </div>
    </form>
  </mat-step>

  <mat-step [stepControl]="secondFormGroup">
    <form [formGroup]="secondFormGroup">
      <ng-template matStepLabel>Mot De Passe</ng-template>
      <div class="ng">
      <mat-form-field class="ng3" appearance="fill">
        <mat-label>Mot de passe</mat-label>
        <input 
        matInput 
        [type]="hidePassword ? 'password' : 'text'" 
        formControlName="password"
        (input)="checkPassword()" 
        required />
        <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" [attr.aria-label]="'Show password'" type="button">
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="secondFormGroup.get('password')?.hasError('required')">
          Le mot de passe est requis !!!
        </mat-error>
        <!-- <mat-error *ngIf="secondFormGroup.get('password')?.hasError('pattern') && secondFormGroup.get('password')?.touched">
        Le mot de passe doit contenir au moins 4 caractères(une lettre majuscule), un chiffre, et un caractère spécial.
        </mat-error> -->
      </mat-form-field>
      <ul class="list-unstyled small">
        <li [ngClass]="{'valid': rules.uppercase, 'invalid': !rules.uppercase}">
          <span>{{ rules.uppercase ? '✓' : '✗' }}</span> Une lettre <strong>majuscule</strong>
        </li>
        <li [ngClass]="{'valid': rules.lowercase, 'invalid': !rules.lowercase}">
          <span>{{ rules.lowercase ? '✓' : '✗' }}</span> Une lettre <strong>minuscule</strong>
        </li>
        <li [ngClass]="{'valid': rules.number, 'invalid': !rules.number}">
          <span>{{ rules.number ? '✓' : '✗' }}</span> Un <strong>chiffre</strong>
        </li>
        <li [ngClass]="{'valid': rules.special, 'invalid': !rules.special}">
          <span>{{ rules.special ? '✓' : '✗' }}</span> Un <strong>caractère spécial</strong>
        </li>
        <li [ngClass]="{'valid': rules.length, 'invalid': !rules.length}">
          <span>{{ rules.length ? '✓' : '✗' }}</span> Minimum <strong>8 caractères</strong>
        </li>
      </ul>
      
      <mat-form-field appearance="fill">
        <mat-label>Confirmer le mot de passe</mat-label>
        <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" required />
        <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" [attr.aria-label]="'Show confirm password'" type="button">
          <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="secondFormGroup.get('confirmPassword')?.hasError('required')">
          La confirmation est requise !!!
        </mat-error>
        <mat-error *ngIf="secondFormGroup.hasError('passwordMismatch')">
          Les mots de passe ne correspondent pas !!!
        </mat-error>
      </mat-form-field>
      

      <div>
        <button mat-button matStepperPrevious class="btn btn-next">Retour</button>
        <button mat-button matStepperNext [disabled]="secondFormGroup.invalid" class="btn btn-next">Suivant</button>
      </div>
    </div>
    </form>
  </mat-step>

  <mat-step [stepControl]="profileFormGroup">
    <form [formGroup]="profileFormGroup">
      <ng-template matStepLabel>Profil</ng-template>
<div class="">
      <h3>Choisissez votre type de profil</h3>
      <div class="cards-container">
        <!-- Carte Recruteur -->
        <mat-card 
          class="profile-card" 
          [class.selected]="profileFormGroup.get('userType')?.value === 'recruiter'"
          (click)="selectUserType('recruiter')">
          <mat-icon>business</mat-icon>
          <h3>Recruteur</h3>
          <p>Je veux publier des offres</p>
        </mat-card>
  
        <!-- Carte Candidat -->
        <mat-card 
          class="profile-card" 
          [class.selected]="profileFormGroup.get('userType')?.value === 'candidate'"
          (click)="selectUserType('candidate')">
          <mat-icon>person</mat-icon>
          <h3>Candidat</h3>
          <p>Je cherche un emploi</p>
        </mat-card>
      </div>
  
      <!-- Section CV -->
      <div *ngIf="profileFormGroup.get('userType')?.value === 'candidate'" class="cv-section">
        <input 
          type="file" 
          #cvInput 
          (change)="onCVSelected($event)" 
          accept=".pdf" 
          hidden
          required>
        
        <button 
          mat-raised-button 
          type="button"
          (click)="cvInput.click()"
          [color]="profileFormGroup.get('cv')?.value ? 'accent' : 'primary'">
          <mat-icon>upload</mat-icon>
          {{ profileFormGroup.get('cv')?.value ? 'CV téléversé' : 'Téléverser CV' }}
        </button>
  
        <div *ngIf="profileFormGroup.get('cv')?.value" class="cv-info">
          <mat-icon>description</mat-icon>
          {{ profileFormGroup.get('cv')?.value.name }}
          <button mat-icon-button type="button" (click)="removeCV()">
            <mat-icon>close</mat-icon>
          </button>
        </div>
  
        <mat-error *ngIf="profileFormGroup.get('cv')?.hasError('required')">
          Le CV est obligatoire pour les candidats
        </mat-error>
      </div>
  
      <div class="step-actions ng3">
        <button mat-button (click)="stepper.reset()" class="btn btn-next">Réinitialiser</button>
        <button mat-button matStepperPrevious class="btn btn-next">Retour</button>
        <button mat-button [disabled]="profileFormGroup.invalid" (click)="enregistrer()" (click)="onSubmit()" class="btn btn-next">Valider</button>
      </div>
    </div>
    </form>
  </mat-step>
  
  <!-- <mat-step>
    <ng-template matStepLabel>Fini</ng-template>
    <h1 style="color: lightgreen">Inscription terminée avec succès !</h1>
    <p style="color: lightgreen">Merci d’avoir rempli le formulaire.</p>
    <button mat-raised-button color="primary" (click)="onSubmit()" >S'inscrire</button>
  </mat-step>   -->
</mat-horizontal-stepper>
</div>
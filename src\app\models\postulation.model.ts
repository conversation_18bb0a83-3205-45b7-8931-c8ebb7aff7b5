export interface PostulationRequest {
  offreId: number;
  lettreMotivation: string;
  cv?: File;
}

export interface PostulationResponse {
  id: number;
  candidatId: number;
  offreId: number;
  lettreMotivation: string;
  scoreInitial: number;
  feedbackInitial: string;
  scoreCompetence: number;
  scoreExperience: number;
  scoreFormation: number;
  scoreEntretien: number;
  feedbackEntretien: string;
  statut: string;
  datePostulation: string;
  dateModification: string;
  dateLimiteConfirmation: string;
}

export enum StatutCandidature {
  EN_ATTENTE = 'EN_ATTENTE',
  EN_ATTENTE_ENTRETIEN = 'EN_ATTENTE_ENTRETIEN',
  EN_ENTRETIEN = 'EN_ENTRETIEN',
  ACCEPTEE = 'ACCEPTEE',
  REFUSEE = 'REFUSEE',
  FINALISEE = 'FINALISEE'
} 
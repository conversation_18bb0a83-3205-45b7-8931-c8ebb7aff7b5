import { Component , OnInit , ViewChild , TemplateRef , ViewEncapsulation } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatDialog , MatDialogRef } from '@angular/material/dialog';
import { ThemePalette } from '@angular/material/core';

interface Skill {
  name: string;
  level: number;
}

interface Candidate {
  id: number;
  name: string;
  position: string;
  score: number;
  decision: string;
  skills?: Skill[];
  notes?: string;
}

@Component({
  selector: 'app-interview-results',
  templateUrl: './interview-results.component.html',
  styleUrls: ['./interview-results.component.scss'] ,
})

// export class InterviewResultsComponent {
//     constructor(private titleService: Title){}
//     ngOnInit(){
//       this.titleService.setTitle('Résultats Entretiens');
//     }
// }

export class InterviewResultsComponent{

  @ViewChild('detailsDialog') detailsDialog!: TemplateRef<any>;
  @ViewChild(MatSort) sort!: MatSort;

  searchText: string = '';
  selectedCandidate: Candidate | null = null;

  candidates: Candidate[] = [
    { 
      id: 1,
      name: 'Marie Durand', 
      position: 'Développeur Full-Stack', 
      score: 82, 
      decision: 'Admissible',
      skills: [
        { name: 'JavaScript', level: 85 },
        { name: 'Angular', level: 78 },
        { name: 'Node.js', level: 80 },
        { name: 'SQL', level: 75 }
      ],
      notes: 'Excellente candidate avec une bonne expérience en développement web. Très à l\'aise avec les frameworks modernes.'
    },
    { 
      id: 2,
      name: 'Jules Lefevre', 
      position: 'Data Scientist', 
      score: 67, 
      decision: 'Entretien requis',
      skills: [
        { name: 'Python', level: 70 },
        { name: 'Machine Learning', level: 65 },
        { name: 'SQL', level: 75 },
        { name: 'Data Visualization', level: 60 }
      ],
      notes: 'Profil intéressant mais manque d\'expérience pratique. Entretien nécessaire pour évaluer la motivation et les compétences techniques.'
    },
    { 
      id: 3,
      name: 'Emma Bertrand', 
      position: 'UI/UX Designer', 
      score: 78, 
      decision: 'Admissible',
      skills: [
        { name: 'Figma', level: 90 },
        { name: 'Adobe XD', level: 85 },
        { name: 'HTML/CSS', level: 70 },
        { name: 'User Research', level: 75 }
      ],
      notes: 'Portfolio impressionnant avec d\'excellentes compétences en design. Bonne compréhension des principes UX.'
    },
    { 
      id: 4,
      name: 'Paul Moreau', 
      position: 'DevOps Engineer', 
      score: 45, 
      decision: 'Refusé',
      skills: [
        { name: 'Docker', level: 50 },
        { name: 'Kubernetes', level: 40 },
        { name: 'CI/CD', level: 45 },
        { name: 'Cloud Platforms', level: 55 }
      ],
      notes: 'Compétences techniques insuffisantes pour le poste. Manque d\'expérience significative avec les outils DevOps requis.'
    }
  ];

  constructor(private titleService: Title , private dialog: MatDialog){}
  ngOnInit() {
    this.titleService.setTitle('Résultats Entretiens');
    this.candidates.sort((a, b) => b.score - a.score);
  }

  get filteredCandidates(): Candidate[] {
    const search = this.searchText.toLowerCase();
  
    return this.candidates.filter(candidate =>
      candidate.name.toLowerCase().includes(search) ||
      candidate.position.toLowerCase().includes(search) ||
      candidate.decision.toLowerCase().includes(search) ||
      candidate.id.toString().includes(search) ||
      candidate.score.toString().includes(search)
    );
  }

  // getScoreClass(score: number): string {
  //   if (score >= 80) return 'score-high';
  //   if (score >= 70) return 'score-medium';
  //   if (score >= 60) return 'score-average';
  //   return 'score-low';
  // }

  // getDecisionClass(decision: string): string {
  //   if (decision === 'Admissible') return 'decision-admitted';
  //   if (decision === 'Entretien requis') return 'decision-interview';
  //   if (decision === 'Refusé') return 'decision-rejected';
  //   return '';
  // }

  
  // getSkillColor(level: number): ThemePalette {
  //   if (level >= 80) return 'primary';
  //   if (level >= 60) return 'accent';
  //   return 'warn';
  // }
  getScoreClass(score: number): string {
    if (score >= 80) return 'score-high';            // Admissible
    if (score > 50) return 'score-medium';           // Entretien requis
    return 'score-low';                              // Refusé
  }
  
  getDecisionClass(score: number): string {
    if (score >= 80) return 'decision-admitted';     // Admissible
    if (score > 50) return 'decision-interview';     // Entretien requis
    return 'decision-rejected';                      // Refusé
  }
  
  getSkillColor(level: number): ThemePalette {
    if (level >= 80) return 'primary';
    if (level >= 60) return 'accent';
    return 'warn';
  }
  
  getDecisionLabel(score: number): string {
    if (score >= 80) return 'Admissible';
    if (score > 50) return 'Entretien requis';
    return 'Refusé';
  }
  

  

  viewDetails(candidate: Candidate): void {
    this.selectedCandidate = candidate;
    this.dialog.open(this.detailsDialog, {
      width: '600px',
      panelClass: 'custom-dialog'
    });
  }

}


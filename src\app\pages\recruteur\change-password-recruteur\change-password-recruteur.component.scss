// Styles pour le header (réutilisé depuis d'autres composants)
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: transparent;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar {
  padding: 0rem 1rem;
  border: none;
}

.logo-container {
  display: flex;
  align-items: center;
  border: none;
}

.logo-image {
  max-height: 100px;
  max-width: auto;
}

.nav-item {
  color: #333;
  font-weight: 500;
  text-decoration: none;

  &:hover {
    color: #2196f3;
  }
}

.profile-menu-container {
  position: relative;
  border: none;
}

.profile-button {
  padding: 0;
  background: none;
  border: none;
}

.profile-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: #3D0B37;
  background-color: #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
  border: none;
}

.underline-hover {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 2.5rem;
    bottom: 0;
    left: 0;
    width: 0%;
    height: 2px;
    background-color: #2196f3;
    transition: width 0.3s ease;
  }

  &:hover::after {
    width: 100%;
  }
}

// Styles principaux pour le composant (thème recruteur avec gradient orange/violet)
.change-password-container {
  min-height: calc(100vh - 100px);
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.form-wrapper {
  background: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .header-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    color: #ff6a00;
    margin-bottom: 1rem;
  }

  h1 {
    color: white;
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: whitesmoke;
    font-size: 1rem;
    margin: 0;
  }
}

.password-form {
  .full-width {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  .mat-form-field {
    &.mat-form-field-appearance-outline {
      // Bordures par défaut (état normal)
      .mat-form-field-outline {
        border-radius: 8px;
        border-color: rgba(255, 255, 255, 0.5) !important;
        border-width: 1px;
      }

      .mat-form-field-outline-thick {
        border-color: rgba(255, 255, 255, 0.5) !important;
        border-width: 2px;
      }

      // État focus (quand on clique sur l'input) - FORCER BLANC
      &.mat-focused {
        .mat-form-field-outline-thick {
          color: white !important;
          border-color: white !important;
          border-width: 2px !important;
          box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
        }

        .mat-form-field-outline-start,
        .mat-form-field-outline-end,
        .mat-form-field-outline-gap {
          border-color: white !important;
        }

        // Label qui remonte (couleur blanche)
        .mat-form-field-label {
          color: white !important;
          font-weight: 500;
        }
      }

      // État hover (survol)
      &:hover:not(.mat-focused) {
        .mat-form-field-outline {
          border-color: rgba(255, 255, 255, 0.8) !important;
        }
      }

      // Quand le champ a une valeur (label reste en haut)
      &.mat-form-field-should-float {
        .mat-form-field-label {
          color: rgba(255, 255, 255, 0.9) !important;
        }
      }
    }

    // Couleur du texte saisi
    .mat-input-element {
      color: white !important;
      caret-color: white !important;
      font-weight: 400;
    }

    // Placeholder (quand pas de label ou en complément)
    .mat-input-element::placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
      opacity: 1;
    }
  }

  // Label par défaut (position basse)
  .mat-form-field-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400;
  }

  .mat-hint {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.5rem;
  }

  .mat-error {
    font-size: 0.875rem;
    color: #f44336;
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: space-between;

  .cancel-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    color: #666;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      border-color: #ff6a00;
      color: #ff6a00;
    }
  }

  .submit-btn {
    flex: 2;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    background: linear-gradient(45deg, #ff6a00, #ee0979);
    color: white;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: linear-gradient(45deg, #ee0979, #d50000);
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(255, 106, 0, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .spinner {
      margin-right: 0.5rem;
    }
  }
}

.security-tips {
  margin-top: 2.5rem;
  padding: 1.5rem;
  background: transparent;
  border-radius: 8px;
  border-left: 4px solid #ff6a00;

  h3 {
    color: black;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    mat-icon {
      color: #ff6a00;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }

  ul {
    margin: 0;
    padding-left: 1.5rem;

    li {
      color: white;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .change-password-container {
    padding: 1rem;
  }

  .form-wrapper {
    padding: 2rem 1.5rem;
  }

  .form-header {
    margin-bottom: 2rem;

    h1 {
      font-size: 1.5rem;
    }
  }

  .form-actions {
    flex-direction: column;

    .cancel-btn,
    .submit-btn {
      flex: none;
      width: 100%;
    }
  }
}

// Animation pour les erreurs
.mat-error {
  animation: slideInError 0.3s ease-out;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Styles pour les icônes de visibilité
.mat-icon-button {
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .mat-icon {
    color: rgba(255, 255, 255, 0.8) !important;
  }
}

// Styles pour tous les icônes dans les form fields
.mat-form-field .mat-icon {
  color: rgba(255, 255, 255, 0.8) !important;
}

// Amélioration de l'accessibilité
.mat-form-field-appearance-outline .mat-form-field-outline {
  transition: color 0.3s ease;
}

.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  border-width: 2px;
}

// Styles pour le spinner de chargement
.mat-spinner {
  display: inline-block;
  vertical-align: middle;
}
// Container principal
.reset-password-container {
  min-height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
}

// Carte principale
.reset-card {
  background: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 3rem;
  max-width: 450px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  overflow: hidden;

  // Effet de brillance
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

// Header de la carte
.card-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .logo-container {
    margin-bottom: 1.5rem;

    .logo {
      max-height: 80px;
      width: auto;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }
  }

  h1 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    background: linear-gradient(45deg, #030303, #090909);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    color: white;
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
  }
}

// Formulaire
.reset-form {
  margin-bottom: 2rem;

  .full-width {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  .mat-form-field {
    &.mat-form-field-appearance-outline {
      // Bordures par défaut (état normal)
      .mat-form-field-outline {
        border-radius: 12px;
        border-width: 1px;
        border-color: rgba(255, 255, 255, 0.5) !important;
      }

      .mat-form-field-outline-thick {
        border-color: rgba(255, 255, 255, 0.5) !important;
        border-width: 2px;
      }

      // État focus (quand on clique sur l'input) - FORCER BLANC
      &.mat-focused {
        .mat-form-field-outline-thick {
          color: white !important;
          border-color: white !important;
          border-width: 2px !important;
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
        }

        .mat-form-field-outline-start,
        .mat-form-field-outline-end,
        .mat-form-field-outline-gap {
          border-color: white !important;
        }

        // Label qui remonte (couleur blanche)
        .mat-form-field-label {
          color: white !important;
          font-weight: 500;
        }
      }

      // État hover (survol)
      &:hover:not(.mat-focused) {
        .mat-form-field-outline {
          border-color: rgba(255, 255, 255, 0.8) !important;
        }
      }

      // Quand le champ a une valeur (label reste en haut)
      &.mat-form-field-should-float {
        .mat-form-field-label {
          color: rgba(255, 255, 255, 0.9) !important;
        }
      }
    }

    // Couleur du texte saisi
    .mat-input-element {
      color: white !important;
      caret-color: white !important;
      font-weight: 400;
    }

    // Placeholder (quand pas de label ou en complément)
    .mat-input-element::placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
      opacity: 1;
    }
  }

  // Label par défaut (position basse)
  .mat-form-field-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400;
  }

  .mat-error {
    font-size: 0.875rem;
    color: #f44336;
    animation: slideInError 0.3s ease-out;
  }

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover:not(:disabled) {
      background: linear-gradient(45deg, #5a6fd8, #6a42a0);
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .spinner {
      margin-right: 0.5rem;
    }

    mat-icon {
      font-size: 1.2rem;
    }
  }
}

// Liens de navigation
.navigation-links {
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
  margin-bottom: 2rem;

  .link-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;

    &:last-child {
      margin-bottom: 0;
    }

    span {
      color: white;
      font-size: 0.9rem;
    }

    .nav-link {
      font-weight: 600;
      text-transform: none;
      padding: 0.25rem 0.75rem;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgb(103, 6, 106);
      }
    }
  }
}

// Section d'informations
.info-section {
  background:transparent;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid red;

  .info-icon {
    color: red;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .info-content {
    h3 {
      color: black;
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        color: white;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}



// Animations
@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .reset-password-container {
    padding: 1rem;
  }

  .reset-card {
    padding: 2rem 1.5rem;
    margin: 1rem 0;
  }

  .card-header {
    margin-bottom: 2rem;

    h1 {
      font-size: 1.5rem;
    }

    .logo {
      max-height: 60px;
    }
  }

  .navigation-links {
    .link-item {
      flex-direction: column;
      gap: 0.25rem;
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .reset-card {
    padding: 1.5rem 1rem;
  }

  .card-header {
    h1 {
      font-size: 1.25rem;
    }
  }

  .submit-btn {
    height: 45px;
    font-size: 0.9rem;
  }
}

// Amélioration de l'accessibilité
.mat-form-field-appearance-outline .mat-form-field-outline {
  transition: all 0.3s ease;
}

.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  border-width: 2px;
}

// Styles pour les icônes Material
.mat-icon {
  &.mat-form-field-suffix {
    color: #999;
    transition: color 0.3s ease;
  }
}

.mat-form-field.mat-focused .mat-icon.mat-form-field-suffix {
  color: white !important;
}

// Styles pour tous les icônes dans les form fields
.mat-form-field .mat-icon {
  color: rgba(255, 255, 255, 0.8) !important;
}

// Spinner de chargement
.mat-spinner {
  display: inline-block;
  vertical-align: middle;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container-t {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}
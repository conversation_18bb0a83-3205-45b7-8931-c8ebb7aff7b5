.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
:host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}

.valid {
  color: #4CAF50;
}

.invalid {
  color: #f44336;
}

ul {
  padding-left: 1rem;
  // margin-top: 0.5rem;
}

li {
  display: flex;
  align-items: center;
  gap: 5px;
}

// --------------------------------------------------------------------------------

// .stepper-container {
//   width: 100%;
//   max-width: 800px;
//   margin: 0 auto;
//   padding: 20px;
//   background-color: transparent;
//   border-radius: 8px;
//   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
// }

// .logo-container {
//   display: flex;
//   justify-content: center;
//   margin-bottom: 24px;
// }

// .header-logo {
//   max-height: 100px;
//   width: auto;
// }

// ---------------------------------------------------------------------

::ng-deep .mat-step-header .mat-step-label {
  color: white; // couleur de base des titres
  white-space: normal; // Empêche les titres d'être tronqués
  overflow: visible; // Empêche les titres d'être tronqués
}

::ng-deep .mat-step-header .mat-step-label .step-label-text {
  color: white; // couleur blanche pour tous les titres
}
::ng-deep .mat-step-header.mat-active .mat-step-label .step-label-selected {
  color: white; // titre de l'étape active (cyan clair)
}

::ng-deep .mat-step-header.mat-active .mat-step-label {
  color: #00ffff; // titre de l'étape active (cyan clair)
}

::ng-deep .mat-step-header.mat-active .mat-step-label .step-label-text {
  color: #00ffff; // titre de l'étape active (cyan clair)
}

// ::ng-deep .mat-step-header.mat-completed .mat-step-label {
//   color: #00ff00; // titre des étapes déjà complétées (vert)
// }

::ng-deep .mat-step-icon {
  background-color: #6a1b9a; // fond du cercle (étapes non complétées)
  color: white;
}

// --------------------------------------------------------------------

// ::ng-deep .mat-horizontal-stepper-header {
//   height: 72px;
  
//   &::after, &::before {
//     border-top-color: rgba(0, 0, 0, 0.12);
//   }
// }
// ::ng-deep .mat-step-icon {
//   background-color: #673ab7;
//   color: white;
// }
// ::ng-deep .mat-step-icon-selected {
//   background-color: #673ab7;
// }
// ::ng-deep .mat-step-header .mat-step-label.mat-step-label-active {
//   color: #673ab7;
// }

// ::ng-deep .mat-step-icon.mat-step-icon-selected {
//   background-color: #00bcd4; // fond de l’étape active
// }

// ::ng-deep .mat-step-icon.mat-step-icon-state-profil {
//   background-color: #4caf50; // fond des étapes complétées
// }

// S'assurer que le stepper s'affiche correctement sur mobile
@media (max-width: 768px) {
  ::ng-deep .mat-horizontal-stepper-header-container {
    flex-direction: row;
    align-items: center;
  }
  
  ::ng-deep .mat-horizontal-stepper-header {
    padding: 0 12px;
    
    .mat-step-label {
      min-width: auto;
      font-size: 12px;
    }
  }
}

// ------------------------------------------------------------------------
// ------------------------------------------------------------------------
// .mat-step-content {
//   min-height: 300px;
// }

mat-form-field {
  width: 100%;
  height: auto;
  margin-bottom: 18px;
  border-radius: 20px;
}

// Masquer les icônes automatiques des navigateurs pour les champs password
input[type="password"] {
  &::-ms-reveal,
  &::-ms-clear {
    display: none !important;
  }

  &::-webkit-credentials-auto-fill-button,
  &::-webkit-strong-password-auto-fill-button {
    display: none !important;
  }
}

// Masquer les icônes automatiques pour les champs text qui étaient des password
input[formControlName="password"],
input[formControlName="confirmPassword"] {
  &::-ms-reveal,
  &::-ms-clear {
    display: none !important;
  }

  &::-webkit-credentials-auto-fill-button,
  &::-webkit-strong-password-auto-fill-button {
    display: none !important;
  }

  // Masquer l'icône de suggestion de mot de passe fort
  &::-webkit-textfield-decoration-container {
    display: none !important;
  }
}

button {
  margin-right: 10px;
}

// Styles spécifiques pour les boutons d'icônes de visibilité
.mat-form-field {
  .mat-icon-button {
    &[matSuffix] {
      // S'assurer que notre bouton d'icône est bien visible
      position: relative;
      z-index: 10;

      .mat-icon {
        color: rgba(255, 255, 255, 0.7);
        transition: color 0.3s ease;

        &:hover {
          color: white;
        }
      }
    }
  }

  // Masquer toute icône automatique qui pourrait apparaître
  .mat-form-field-suffix {
    .mat-icon:not(.mat-icon-button .mat-icon) {
      display: none !important;
    }
  }
}

mat-horizontal-stepper {
  padding: 2rem;
  max-width: 500px;
  height: 560px;
  min-width: auto;
  margin: 4rem auto;
  border-radius: 10px;
  background: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
}

.step-center-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.step-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  // color: #ffffff;
}

.ng{
  margin-top: 3rem;
}
.ng2{
  margin-top: 4rem;
}
.ng3{
  // padding-top: 1.5rem;
  margin-bottom: 6px;
}




.cards-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.profile-card {
  width: 200px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, #910d74 0%, #8f087d 100%);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }

  &.selected {
    border: 2px solid #ffffff;
    background-color: rgba(63, 81, 181, 0.05);
  }

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: blue;
  }
}

.cv-section {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  button {
    margin-top: 10px;
  }
}

.cv-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.step-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
h3{
  color: #ffffff;
}
p{
  color: rgb(48, 158, 158);
  font-size: large;
}
// .logo-stepper-container {
//   display: flex;
//   justify-content:first baseline;
//   margin-bottom: 16px;
// }

// .logo-stepper-img {
//   width: 100px;
//   height: auto;
//   object-fit: contain;
//   filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
// }



.btn-next {
  background-color: transparent !important;
  color: black; // texte violet foncé ou selon ton thème
  // border: 1px solid #6a1b9a; // facultatif : pour un contour stylé
  font-weight: 600;
  transition: 0.3s;
}

.btn-next:hover {
  background-color: transparent;
  color:white;
  box-shadow: black;
}

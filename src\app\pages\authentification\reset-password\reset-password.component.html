<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container-t">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>
<!-- Container principal -->
<div class="reset-password-container">
  <!-- Carte du formulaire -->
  <div class="reset-card">
    <!-- Header avec logo et titre -->
    <div class="card-header">
      <div class="logo-container">
        <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo">
      </div>
      <h1>Réinitialiser le mot de passe</h1><br>
      <p class="subtitle">
        Entrez votre adresse email pour recevoir un lien de réinitialisation
      </p>
    </div>

    <!-- Formulaire -->
    <form [formGroup]="resetForm" (ngSubmit)="onSubmit()" class="reset-form">

      <!-- Champ Email -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Adresse email</mat-label>
        <input matInput
               type="email"
               formControlName="email"
               placeholder="<EMAIL>"
               autocomplete="email">
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="hasError('email')">
          {{ getErrorMessage('email') }}
        </mat-error>
      </mat-form-field>

      <!-- Bouton Envoyer -->
      <button mat-raised-button
              type="submit"
              color="primary"
              class="submit-btn"
              [disabled]="resetForm.invalid || isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
        <mat-icon *ngIf="!isLoading">send</mat-icon>
        <span *ngIf="!isLoading">Envoyer le lien</span>
        <span *ngIf="isLoading">Envoi en cours...</span>
      </button>
    </form>

    <!-- Liens de navigation -->
    <div class="navigation-links">
      <div class="link-item">
        <span>Vous vous souvenez de votre mot de passe ?</span>
        <button mat-button
                color="primary"
                (click)="goToLogin()"
                class="nav-link">
          Se connecter
        </button>
      </div>

      <div class="link-item">
        <span>Pas encore de compte ?</span>
        <button mat-button
                color="accent"
                (click)="goToRegister()"
                class="nav-link">
          S'inscrire
        </button>
      </div>
    </div>

    <!-- Informations supplémentaires -->
    <div class="info-section">
      <mat-icon class="info-icon">info</mat-icon>
      <div class="info-content">
        <h3>Comment ça marche ?</h3>
        <ul>
          <li>Entrez votre adresse email ci-dessus</li>
          <li>Cliquez sur "Envoyer le lien"</li>
          <li>Vérifiez votre boîte de réception</li>
          <li>Suivez le lien pour créer un nouveau mot de passe</li>
        </ul>
      </div>
    </div>
  </div>
</div>

import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Offre } from '../../services/offre.service';
import { PostulationService, PostulationRequest } from '../../services/postulation.service';
import { AlertsService } from '../../services/alerts.service';

@Component({
  selector: 'app-postulation-dialog',
  template: `
    <h2 mat-dialog-title>Postuler à l'offre</h2>
    <mat-dialog-content>
      <form [formGroup]="postulationForm" (ngSubmit)="onSubmit()">
        <div class="form-container">
          <div class="offre-info">
            <h3>{{ data.offre.titre }}</h3>
            <p>Recruteur ID: {{ data.offre.recruteurId }} • {{ data.offre.ville }}</p>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Lettre de motivation</mat-label>
            <textarea matInput
                      formControlName="lettreMotivation"
                      rows="6"
                      placeholder="Expliquez pourquoi vous êtes le candidat idéal pour ce poste..."></textarea>
            <mat-error *ngIf="postulationForm.get('lettreMotivation')?.hasError('required')">
              La lettre de motivation est requise
            </mat-error>
            <mat-error *ngIf="postulationForm.get('lettreMotivation')?.hasError('minlength')">
              La lettre de motivation doit faire au moins 100 caractères
            </mat-error>
          </mat-form-field>

          <div class="cv-upload">
            <label class="upload-label">
              <input type="file"
                     (change)="onFileSelected($event)"
                     accept=".pdf,.doc,.docx"
                     #fileInput
                     style="display: none">
              <button type="button"
                      mat-stroked-button
                      color="primary"
                      (click)="fileInput.click()">
                <mat-icon>upload_file</mat-icon>
                {{ selectedFile ? selectedFile.name : 'Télécharger votre CV' }}
              </button>
            </label>
            <p class="file-hint">Formats acceptés : PDF, DOC, DOCX (max 5MB)</p>
            <mat-error *ngIf="fileError">{{ fileError }}</mat-error>
          </div>
        </div>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Annuler</button>
      <button mat-raised-button
              color="primary"
              [disabled]="!postulationForm.valid || !selectedFile"
              (click)="onSubmit()">
        Envoyer ma candidature
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .form-container {
      padding: 1rem 0;
    }

    .offre-info {
      margin-bottom: 1.5rem;
      padding: 1rem;
      background-color: #f5f5f5;
      border-radius: 4px;

      h3 {
        margin: 0 0 0.5rem;
        color: #333;
        font-size: 1.2rem;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
      }
    }

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .cv-upload {
      margin-top: 1rem;

      .upload-label {
        display: block;
        margin-bottom: 0.5rem;
      }

      .file-hint {
        margin: 0.5rem 0 0;
        font-size: 0.8rem;
        color: #666;
      }
    }

    mat-dialog-actions {
      padding: 1rem 0 0;
      margin: 0;
    }
  `]
})
export class PostulationDialogComponent implements OnInit {
  postulationForm: FormGroup;
  selectedFile: File | null = null;
  fileError: string | null = null;
  readonly maxFileSize = 5 * 1024 * 1024; // 5MB

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<PostulationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { offre: Offre },
    private postulationService: PostulationService,
    private alertsService: AlertsService
  ) {
    this.postulationForm = this.fb.group({
      lettreMotivation: ['', [Validators.required, Validators.minLength(100)]]
    });
  }

  ngOnInit(): void {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      
      // Vérification du type de fichier
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        this.fileError = 'Format de fichier non supporté. Veuillez télécharger un fichier PDF ou Word.';
        this.selectedFile = null;
        return;
      }

      // Vérification de la taille du fichier
      if (file.size > this.maxFileSize) {
        this.fileError = 'Le fichier est trop volumineux. Taille maximale : 5MB.';
        this.selectedFile = null;
        return;
      }

      this.selectedFile = file;
      this.fileError = null;
    }
  }

  onSubmit(): void {
    if (this.postulationForm.valid && this.selectedFile) {
      const request: PostulationRequest = {
        offreId: this.data.offre.id,
        lettreMotivation: this.postulationForm.get('lettreMotivation')?.value,
        cv: this.selectedFile
      };

      this.postulationService.postuler(request).subscribe({
        next: (response) => {
          this.alertsService.showSuccess('Votre candidature a été envoyée avec succès !');
          this.dialogRef.close(response);
        },
        error: (error) => {
          this.alertsService.showError('Une erreur est survenue lors de l\'envoi de votre candidature.');
          console.error('Erreur lors de la postulation:', error);
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
} 
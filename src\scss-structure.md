# Structure SCSS - Background Global

## Concept
L'image `bg.png` est définie comme arrière-plan global de toute l'application dans `styles.scss`. Tous les autres composants gardent leurs styles normaux et s'affichent par-dessus cette image de fond.

## Configuration actuelle

### styles.scss
- **Background global** : `background-image: url('/assets/images/bg.png')` sur le `body`
- **Propriétés** :
  - `background-attachment: fixed` - L'image reste fixe lors du scroll
  - `background-size: cover` - L'image couvre toute la zone
  - `background-position: center` - Centrée
  - `background-repeat: no-repeat` - Pas de répétition

### Layout
- `.app-container` : Conteneur principal avec `min-height: 100vh`
- `.main-content` : Contenu principal avec `padding-top: 90px` pour le header

## Avantages
1. **Image unique** : Une seule image de fond pour toute l'application
2. **Performance** : L'image est chargée une seule fois
3. **Cohérence** : Même arrière-plan sur toutes les pages
4. **Simplicité** : Les composants gardent leurs styles existants

## Composants inchangés
- Tous les formulaires gardent leurs styles normaux
- Les cartes Material Design restent comme avant
- Les dialogues et modals conservent leur apparence
- Les pages d'authentification gardent leurs backgrounds spécifiques

## Résultat
L'image `bg.png` s'affiche en arrière-plan de toute l'application, et tous les contenus (header, footer, pages, composants) s'affichent normalement par-dessus cette image.

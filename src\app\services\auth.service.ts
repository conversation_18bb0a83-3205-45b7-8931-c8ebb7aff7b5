import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { JwtHelperService } from '@auth0/angular-jwt';
import { AuthResponse, LoginCredentials, RefreshTokenRequest } from '../models/auth.model';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = `${environment.apiUrl}/auth`;
  private currentUserSubject = new BehaviorSubject<any>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private jwtHelper = new JwtHelperService();

  constructor(private http: HttpClient) {
    this.loadUserFromStorage();
  }

  private loadUserFromStorage() {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    if (token && refreshToken && !this.jwtHelper.isTokenExpired(token)) {
      const user = this.jwtHelper.decodeToken(token);
      this.currentUserSubject.next(user);
    } else {
      this.logout();
    }
  }

  login(credentials: LoginCredentials, password: any): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials)
      .pipe(
        tap(response => {
          localStorage.setItem('token', response.token);
          localStorage.setItem('refreshToken', response.refreshToken);
          const user = this.jwtHelper.decodeToken(response.token);
          this.currentUserSubject.next(user);
        })
      );
  }

  register(userData: FormData): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, userData)
      .pipe(
        tap(response => {
          localStorage.setItem('token', response.token);
          localStorage.setItem('refreshToken', response.refreshToken);
          const user = this.jwtHelper.decodeToken(response.token);
          this.currentUserSubject.next(user);
        })
      );
  }

  logout(): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/logout`, {}).pipe(
      tap(() => {
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        this.currentUserSubject.next(null);
      })
    );
  }

  // Méthode pour déconnexion locale (sans appel API)
  logoutLocal() {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    this.currentUserSubject.next(null);
  }

  // Vérifier la validité du token
  verifyToken(): Observable<boolean> {
    return this.http.post<boolean>(`${this.apiUrl}/verify-token`, {});
  }

  isAuthenticated(): Observable<boolean> {
    const token = this.getToken();
    const refreshToken = this.getRefreshToken();
    return this.currentUser$.pipe(
      map(user => !!user && token !== null && !this.jwtHelper.isTokenExpired(token))
    );
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  getUserRole(): string | null {
    const token = this.getToken();
    if (token) {
      const decodedToken = this.jwtHelper.decodeToken(token);
      return decodedToken?.role || null;
    }
    return null;
  }

  refreshToken(): Observable<AuthResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return new Observable(observer => observer.error('No refresh token available'));
    }

    const requestBody: RefreshTokenRequest = { refreshToken };
    return this.http.post<AuthResponse>(`${this.apiUrl}/refresh-token`, requestBody)
      .pipe(
        tap(response => {
          localStorage.setItem('token', response.token);
          localStorage.setItem('refreshToken', response.refreshToken);
          const user = this.jwtHelper.decodeToken(response.token);
          this.currentUserSubject.next(user);
        })
      );
  }

  createCandidat(formData: FormData): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/candidats`, formData);
  }

  getCandidat(id: number): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/candidats/${id}`);
  }

  getAllCandidats(): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/candidats`);
  }

  updateCandidat(id: number, data: any): Observable<any> {
    return this.http.put<any>(`${environment.apiUrl}/candidats/${id}`, data);
  }

  deleteCandidat(id: number): Observable<any> {
    return this.http.delete<any>(`${environment.apiUrl}/candidats/${id}`);
  }

  postuler(data: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/postulations/postuler`, data);
  }

  getPostulationsByCandidat(candidatId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/postulations/candidat/${candidatId}`);
  }

  getPostulationsByOffre(offreId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/postulations/offre/${offreId}`);
  }

  updateStatut(id: number, statut: string): Observable<any> {
    return this.http.put<any>(`${environment.apiUrl}/postulations/${id}/statut`, { statut });
  }

  confirmer(id: number): Observable<any> {
    return this.http.put<any>(`${environment.apiUrl}/postulations/${id}/confirmation`, {});
  }

  accepter(id: number): Observable<any> {
    return this.http.put<any>(`${environment.apiUrl}/postulations/${id}/accepter`, {});
  }

  postulerUpload(formData: FormData): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/postulations/postuler-upload`, formData);
  }

  creerEntretien(data: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/entretiens/creer`, data);
  }

  demarrerEntretien(postulationId: number): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/entretiens/${postulationId}/demarrer`, {});
  }

  repondre(entretienId: number, reponse: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/entretiens/${entretienId}/repondre`, reponse);
  }

  getResultat(entretienId: number): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/entretiens/${entretienId}/resultat`);
  }

  getEntretiensByCandidat(candidatId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/entretiens/candidat/${candidatId}`);
  }

  getEntretiensByOffre(offreId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/entretiens/offre/${offreId}`);
  }

  getById(id: number): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/entretiens/${id}`);
  }

  getPremiereQuestion(id: number): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/entretiens/${id}/premiere-question`);
  }

  getAll(): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/resultats/`);
  }

  getResultatsByCandidat(candidatId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/resultats/candidat/${candidatId}`);
  }

  getResultatsByOffre(offreId: number): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/resultats/offre/${offreId}`);
  }

  create(data: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/resultats/`, data);
  }

  delete(id: number): Observable<any> {
    return this.http.delete<any>(`${environment.apiUrl}/resultats/${id}`);
  }

  getCurrentUserId(): number | null {
    const token = localStorage.getItem('token');
    if (token) {
      const decodedToken = this.jwtHelper.decodeToken(token);
      return decodedToken?.sub ? Number(decodedToken.sub) : null;
    }
    return null;
  }
} 
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Recruteur {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  entreprise: string;
  poste: string;
  secteurActivite: string;
  adresse: string;
  siteWeb?: string;
  linkedinUrl?: string;
  description?: string;
}

export interface ChangePasswordRequest {
  ancienMotDePasse: string;
  nouveauMotDePasse: string;
}

@Injectable({
  providedIn: 'root'
})
export class RecruteurService {
  private apiUrl = `${environment.apiUrl}/recruteurs`;

  constructor(private http: HttpClient) { }

  // Récupérer tous les recruteurs
  getAllRecruteurs(): Observable<Recruteur[]> {
    return this.http.get<Recruteur[]>(`${this.apiUrl}/`);
  }

  // Récupérer un recruteur par ID
  getRecruteurById(id: number): Observable<Recruteur> {
    return this.http.get<Recruteur>(`${this.apiUrl}/${id}`);
  }

  // Créer un nouveau recruteur
  createRecruteur(recruteur: Recruteur): Observable<Recruteur> {
    return this.http.post<Recruteur>(`${this.apiUrl}/`, recruteur);
  }

  // Inscription d'un recruteur
  inscriptionRecruteur(recruteur: Recruteur): Observable<Recruteur> {
    return this.http.post<Recruteur>(`${this.apiUrl}/inscription`, recruteur);
  }

  // Mettre à jour un recruteur
  updateRecruteur(id: number, recruteur: Partial<Recruteur>): Observable<Recruteur> {
    return this.http.put<Recruteur>(`${this.apiUrl}/${id}`, recruteur);
  }

  // Changer le mot de passe
  changePassword(id: number, passwordData: ChangePasswordRequest): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}/mot-de-passe`, null, {
      params: {
        ancienMotDePasse: passwordData.ancienMotDePasse,
        nouveauMotDePasse: passwordData.nouveauMotDePasse
      }
    });
  }

  // Récupérer un recruteur par email
  getRecruteurByEmail(email: string): Observable<Recruteur> {
    return this.http.get<Recruteur>(`${this.apiUrl}/email/${email}`);
  }

  // Supprimer un recruteur
  deleteRecruteur(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}

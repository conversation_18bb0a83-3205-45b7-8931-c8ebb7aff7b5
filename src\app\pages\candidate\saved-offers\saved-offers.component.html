<app-loading [isLoading]="isLoading" [message]="'Chargement de vos offres sauvegardées...'"></app-loading>

<div class="saved-offers-container">
  <div class="saved-offers-header">
    <h1>Offres sauvegardées</h1><br>
    <p>Retrouvez ici toutes les offres que vous avez marquées comme favorites</p>
  </div>

  <div class="offers-grid" *ngIf="savedOffers.length > 0">
    <mat-card class="offer-card" *ngFor="let savedOffer of savedOffers">
      <mat-card-header>
        <mat-card-title>{{ savedOffer.offre.titre }}</mat-card-title>
        <mat-card-subtitle>
          {{ savedOffer.offre.entreprise }} - {{ savedOffer.offre.ville }}
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="offer-details">
          <div class="offer-type">
            <mat-icon>work</mat-icon>
            <span>{{ savedOffer.offre.typeContrat }}</span>
          </div>

          <div class="offer-deadline" [class.expired]="isOfferExpired(savedOffer.offre.dateLimite)">
            <mat-icon>event</mat-icon>
            <span *ngIf="!isOfferExpired(savedOffer.offre.dateLimite)">
              Expire dans {{ getDaysRemaining(savedOffer.offre.dateLimite) }} jours
            </span>
            <span *ngIf="isOfferExpired(savedOffer.offre.dateLimite)">
              Offre expirée
            </span>
          </div>

          <div class="offer-salary" *ngIf="savedOffer.offre.salaire">
            <mat-icon>attach_money</mat-icon>
            <span>{{ savedOffer.offre.salaire }}</span>
          </div>
        </div>

        <div class="offer-description">
          <p>{{ savedOffer.offre.description | slice:0:200 }}...</p>
        </div>

        <div class="offer-skills">
          <mat-chip-list>
            <mat-chip *ngFor="let competence of savedOffer.offre.competences" color="primary" selected>
              {{ competence }}
            </mat-chip>
          </mat-chip-list>
        </div>

        <div class="offer-benefits" *ngIf="savedOffer.offre.avantages && savedOffer.offre.avantages.length > 0">
          <h4>Avantages</h4>
          <ul>
            <li *ngFor="let avantage of savedOffer.offre.avantages">
              <mat-icon>check_circle</mat-icon>
              {{ avantage }}
            </li>
          </ul>
        </div>
      </mat-card-content>

      <mat-card-actions>
        <button mat-button color="primary" (click)="viewOfferDetails(savedOffer.offreId)">
          <mat-icon>visibility</mat-icon>
          Voir l'offre
        </button>
        <button mat-raised-button color="accent" 
                (click)="postuler(savedOffer)"
                [disabled]="isOfferExpired(savedOffer.offre.dateLimite)">
          <mat-icon>send</mat-icon>
          Postuler
        </button>
        <button mat-icon-button color="warn" (click)="removeSavedOffer(savedOffer)">
          <mat-icon>delete</mat-icon>
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Message si aucune offre sauvegardée -->
  <div class="no-saved-offers" *ngIf="savedOffers.length === 0 && !isLoading">
    <mat-icon>bookmark_border</mat-icon>
    <p>Vous n'avez pas encore sauvegardé d'offres</p>
    <button mat-raised-button color="primary" routerLink="/candidate/dashboard">
      Voir les offres disponibles
    </button>
  </div>
</div> 
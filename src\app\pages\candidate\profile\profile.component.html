<app-loading [isLoading]="isLoading" [message]="'Chargement du profil...'"></app-loading>

<div class="profile-container">
  <div class="profile-header">
    <h1>Mon Profil</h1>
    <p>G<PERSON>rez vos informations personnelles, vos expériences et votre formation.</p>
  </div>

  <form [formGroup]="profileForm" (ngSubmit)="saveProfile()" class="profile-form">
    <!-- Section Informations Personnelles -->
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>Informations Personnelles</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="profile-picture-upload">
          <img [src]="profileForm.get('photoUrl')?.value || 'assets/images/default-avatar.png'" alt="Photo de profil">
          <input type="file" #fileInput hidden (change)="onFileSelected($event)" accept="image/*">
          <button mat-stroked-button type="button" (click)="fileInput.click()">
            <mat-icon>cloud_upload</mat-icon> Télécharger une photo
          </button>
        </div>

        <div class="form-grid">
          <mat-form-field appearance="outline">
            <mat-label>Nom</mat-label>
            <input matInput formControlName="nom" required>
            <mat-error *ngIf="profileForm.get('nom')?.hasError('required')">Le nom est requis</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Prénom</mat-label>
            <input matInput formControlName="prenom" required>
            <mat-error *ngIf="profileForm.get('prenom')?.hasError('required')">Le prénom est requis</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" type="email" required>
            <mat-error *ngIf="profileForm.get('email')?.hasError('required')">L'email est requis</mat-error>
            <mat-error *ngIf="profileForm.get('email')?.hasError('email')">Format d'email invalide</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Téléphone</mat-label>
            <input matInput formControlName="telephone">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Adresse</mat-label>
            <input matInput formControlName="adresse">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Date de Naissance</mat-label>
            <input matInput [matDatepicker]="birthDatepicker" formControlName="dateNaissance">
            <mat-datepicker-toggle matSuffix [for]="birthDatepicker"></mat-datepicker-toggle>
            <mat-datepicker #birthDatepicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Genre</mat-label>
            <mat-select formControlName="genre">
              <mat-option value="HOMME">Homme</mat-option>
              <mat-option value="FEMME">Femme</mat-option>
              <mat-option value="AUTRE">Autre</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Nationalité</mat-label>
            <input matInput formControlName="nationalite">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>URL LinkedIn</mat-label>
            <input matInput formControlName="linkedinUrl">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>URL GitHub</mat-label>
            <input matInput formControlName="githubUrl">
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Section Compétences -->
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>Compétences</mat-card-title>
      </mat-card-header><br>
      <mat-card-content>
        <div formArrayName="competences">
          <div *ngFor="let competenceControl of competences.controls; let i = index" class="dynamic-item">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Compétence {{ i + 1 }}</mat-label>
              <input matInput [formControlName]="i" required>
              <mat-error *ngIf="competenceControl.hasError('required')">La compétence est requise</mat-error>
            </mat-form-field>
            <button mat-icon-button type="button" color="warn" (click)="removeCompetenceInput(i)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        <button mat-raised-button type="button" color="accent" (click)="addCompetenceInput()">
          <mat-icon>add</mat-icon> Ajouter une compétence
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Section Expériences Professionnelles -->
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>Expériences Professionnelles</mat-card-title>
      </mat-card-header><br>
      <mat-card-content>
        <div formArrayName="experiencesProfessionnelles">
          <div *ngFor="let experienceGroup of experiencesProfessionnelles.controls; let i = index" 
               [formGroup]="asFormGroup(experienceGroup)" 
               class="dynamic-section">
            <mat-expansion-panel [expanded]="false">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{ experienceGroup.get('titre')?.value || 'Nouvelle Expérience' }}
                </mat-panel-title>
              </mat-expansion-panel-header>
              
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Titre</mat-label>
                  <input matInput formControlName="titre" required>
                  <mat-error *ngIf="experienceGroup.get('titre')?.hasError('required')">Le titre est requis</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Entreprise</mat-label>
                  <input matInput formControlName="entreprise" required>
                  <mat-error *ngIf="experienceGroup.get('entreprise')?.hasError('required')">L'entreprise est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Ville</mat-label>
                  <input matInput formControlName="ville" required>
                  <mat-error *ngIf="experienceGroup.get('ville')?.hasError('required')">La ville est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date de Début</mat-label>
                  <input matInput [matDatepicker]="expStartDatepicker" formControlName="dateDebut" required>
                  <mat-datepicker-toggle matSuffix [for]="expStartDatepicker"></mat-datepicker-toggle>
                  <mat-datepicker #expStartDatepicker></mat-datepicker>
                  <mat-error *ngIf="experienceGroup.get('dateDebut')?.hasError('required')">La date de début est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date de Fin (laissez vide si en cours)</mat-label>
                  <input matInput [matDatepicker]="expEndDatepicker" formControlName="dateFin">
                  <mat-datepicker-toggle matSuffix [for]="expEndDatepicker"></mat-datepicker-toggle>
                  <mat-datepicker #expEndDatepicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"></textarea>
                </mat-form-field>
              </div>
              
              <mat-action-row>
                <button mat-raised-button type="button" color="warn" (click)="removeExperience(i)">
                  <mat-icon>delete</mat-icon> Supprimer
                </button>
              </mat-action-row>
            </mat-expansion-panel>
          </div>
        </div>
        
        <button mat-raised-button type="button" color="accent" (click)="addExperience()">
          <mat-icon>add</mat-icon> Ajouter une expérience
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Section Formations -->
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>Formations</mat-card-title>
      </mat-card-header><br>
      <mat-card-content>
        <div formArrayName="formations">
          <div *ngFor="let formationGroup of formations.controls; let i = index" 
               [formGroup]="asFormGroup(formationGroup)" 
               class="dynamic-section">
            <mat-expansion-panel [expanded]="false">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{ formationGroup.get('titreDiplome')?.value || 'Nouvelle Formation' }}
                </mat-panel-title>
              </mat-expansion-panel-header>
              
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Titre du Diplôme</mat-label>
                  <input matInput formControlName="titreDiplome" required>
                  <mat-error *ngIf="formationGroup.get('titreDiplome')?.hasError('required')">Le titre du diplôme est requis</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Établissement</mat-label>
                  <input matInput formControlName="etablissement" required>
                  <mat-error *ngIf="formationGroup.get('etablissement')?.hasError('required')">L'établissement est requis</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Ville</mat-label>
                  <input matInput formControlName="ville" required>
                  <mat-error *ngIf="formationGroup.get('ville')?.hasError('required')">La ville est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date de Début</mat-label>
                  <input matInput [matDatepicker]="formStartDatepicker" formControlName="dateDebut" required>
                  <mat-datepicker-toggle matSuffix [for]="formStartDatepicker"></mat-datepicker-toggle>
                  <mat-datepicker #formStartDatepicker></mat-datepicker>
                  <mat-error *ngIf="formationGroup.get('dateDebut')?.hasError('required')">La date de début est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date de Fin</mat-label>
                  <input matInput [matDatepicker]="formEndDatepicker" formControlName="dateFin" required>
                  <mat-datepicker-toggle matSuffix [for]="formEndDatepicker"></mat-datepicker-toggle>
                  <mat-datepicker #formEndDatepicker></mat-datepicker>
                  <mat-error *ngIf="formationGroup.get('dateFin')?.hasError('required')">La date de fin est requise</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"></textarea>
                </mat-form-field>
              </div>
              
              <mat-action-row>
                <button mat-raised-button type="button" color="warn" (click)="removeFormation(i)">
                  <mat-icon>delete</mat-icon> Supprimer
                </button>
              </mat-action-row>
            </mat-expansion-panel>
          </div>
        </div>
        
        <button mat-raised-button type="button" color="accent" (click)="addFormation()">
          <mat-icon>add</mat-icon> Ajouter une formation
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Section Langues -->
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>Langues</mat-card-title>
      </mat-card-header><br>
      <mat-card-content>
        <div formArrayName="langues">
          <div *ngFor="let langueGroup of langues.controls; let i = index" 
               [formGroup]="asFormGroup(langueGroup)" 
               class="dynamic-item">
            <mat-form-field appearance="outline">
              <mat-label>Langue {{ i + 1 }}</mat-label>
              <input matInput formControlName="nom" required>
              <mat-error *ngIf="langueGroup.get('nom')?.hasError('required')">Le nom de la langue est requis</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Niveau</mat-label>
              <mat-select formControlName="niveau" required>
                <mat-option value="DEBUTANT">Débutant</mat-option>
                <mat-option value="INTERMEDIAIRE">Intermédiaire</mat-option>
                <mat-option value="AVANCE">Avancé</mat-option>
                <mat-option value="BILINGUE">Bilingue</mat-option>
                <mat-option value="NATIF">Natif</mat-option>
              </mat-select>
              <mat-error *ngIf="langueGroup.get('niveau')?.hasError('required')">Le niveau est requis</mat-error>
            </mat-form-field>

            <button mat-icon-button type="button" color="warn" (click)="removeLangue(i)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        
        <button mat-raised-button type="button" color="accent" (click)="addLangue()">
          <mat-icon>add</mat-icon> Ajouter une langue
        </button>
      </mat-card-content>
    </mat-card>

    <div class="form-actions">
      <button mat-raised-button color="primary" type="submit" [disabled]="isLoading || profileForm.invalid">
        <mat-icon>save</mat-icon> Enregistrer les modifications
      </button>
      <button mat-stroked-button type="button" (click)="loadProfile()" [disabled]="isLoading">
        <mat-icon>refresh</mat-icon> Réinitialiser
      </button>
    </div>
  </form>
</div>
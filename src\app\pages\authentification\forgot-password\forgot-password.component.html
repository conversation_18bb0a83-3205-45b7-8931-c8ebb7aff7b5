<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>

<div class="reset-container">

    <!-- <img src="assets/images/loogo.png" alt="Logo" class="logo" /> -->

    <h2>Réinitialiser le mot de passe</h2>
  
    <form [formGroup]="resetForm" (ngSubmit)="onSubmit()">
  
      <mat-form-field appearance="outline">
        <mat-label>Nouveau mot de passe</mat-label>
        <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required />
        <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="resetForm.get('password')?.hasError('required')">
          Le mot de passe est requis
        </mat-error>
      </mat-form-field>
  
      <mat-form-field appearance="outline">
        <mat-label>Confirmer le mot de passe</mat-label>
        <input matInput [type]="hideConfirm ? 'password' : 'text'" formControlName="confirmPassword" required />
        <button mat-icon-button matSuffix (click)="hideConfirm = !hideConfirm" type="button">
          <mat-icon>{{ hideConfirm ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="resetForm.get('confirmPassword')?.hasError('required')">
          Confirmation requise
        </mat-error>
        <!-- <mat-error *ngIf="resetForm.hasError('passwordMismatch')">
          Les mots de passe ne correspondent pas
        </mat-error> -->
      </mat-form-field>
  
      <button mat-raised-button color="primary" type="submit" [disabled]="resetForm.invalid" (click)="changepassword()">
        Réinitialiser
      </button>
    </form>
  </div>
  

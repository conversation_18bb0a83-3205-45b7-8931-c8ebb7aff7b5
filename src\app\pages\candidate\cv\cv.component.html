<app-loading [isLoading]="isLoading" [message]="'Chargement du CV...'"></app-loading>

<div class="cv-container">
  <div class="cv-header">
    <h1>Mon CV</h1><br>
    <p>Téléchargez et gérez votre CV. Il sera analysé par notre IA pour améliorer vos chances.</p>
  </div>

  <mat-card class="cv-card">
    <mat-card-header>
      <mat-card-title>Télécharger un nouveau CV</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="upload-zone">
        <input type="file" #fileInput hidden (change)="onFileSelected($event)" accept=".pdf,.doc,.docx">
        <button mat-stroked-button type="button" (click)="fileInput.click()">
          <mat-icon>upload_file</mat-icon> Choisir un fichier
        </button>
        <span class="file-name">{{ selectedFile ? selectedFile.name : 'Aucun fichier sélectionné' }}</span>
      </div>
      <mat-hint>Formats acceptés : PDF, DOC, DOCX (taille max 5MB)</mat-hint>
    </mat-card-content>
    <mat-card-actions align="end">
      <button mat-raised-button color="primary" (click)="uploadCv()" [disabled]="!selectedFile || isLoading">
        <mat-icon>cloud_upload</mat-icon> Télécharger le CV
      </button>
    </mat-card-actions>
  </mat-card>

  <mat-card class="cv-card" *ngIf="currentCv">
    <mat-card-header>
      <mat-card-title>Votre CV actuel</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="cv-info-display">
        <p><strong>Nom du fichier :</strong> {{ currentCv.nomFichier }}</p>
        <p><strong>Date de téléchargement :</strong> {{ currentCv.dateUpload | date:'dd/MM/yyyy HH:mm' }}</p>
        <p><strong>Taille :</strong> {{ currentCv.taille / 1024 / 1024 | number:'1.2-2' }} Mo</p>
        <mat-expansion-panel class="cv-text-preview">
          <mat-expansion-panel-header>
            <mat-panel-title>Aperçu du texte extrait</mat-panel-title>
          </mat-expansion-panel-header>
          <mat-panel-content>
            <p class="extracted-text">{{ currentCv.texteExtrait || 'Aucun texte extrait disponible.' }}</p>
          </mat-panel-content>
        </mat-expansion-panel>
      </div>
    </mat-card-content>
    <mat-card-actions align="end">
      <button mat-button color="accent" (click)="downloadCv()" [disabled]="!currentCv.cheminFichier">
        <mat-icon>download</mat-icon> Télécharger
      </button>
      <button mat-raised-button color="warn" (click)="deleteCv()">
        <mat-icon>delete</mat-icon> Supprimer
      </button>
    </mat-card-actions>
  </mat-card>

  <mat-card class="cv-card" *ngIf="!currentCv && !isLoading">
    <mat-card-content class="no-cv-message">
      <mat-icon>info</mat-icon>
      <p>Vous n'avez pas encore téléchargé de CV. Veuillez en télécharger un pour commencer !</p>
    </mat-card-content>
  </mat-card>
</div> 
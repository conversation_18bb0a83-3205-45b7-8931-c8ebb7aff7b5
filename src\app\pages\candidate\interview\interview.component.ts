import { Component, OnInit, Input, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertsService } from '../../../services/alerts.service';
import { EntretienService } from '../../../services/entretien.service';
import { Observable } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import { AbstractControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-interview',
  templateUrl: './interview.component.html',
  styleUrls: ['./interview.component.scss']
})
export class InterviewComponent implements OnInit, OnDestroy {
  aiQuestion: string = "Pouvez-vous vous présenter en quelques mots ?";
  aiSpeaking: boolean = false;
  isListening: boolean = false;
  transcript: string = '';
  recognition: any;
  remainingTime: number = 30;
  maxRecordingTime: number = 30;
  recordingTimer: any;
  partialTranscript: string = '';
  isOnline: boolean = navigator.onLine;
  isProcessing: boolean = false;
  currentQuestionIndex: number = 0;
  totalQuestions: number = 0;
  questions: string[] = [];
  errorMessage: string = '';
  etape: number = 1;
  candidat: any = {
    nom: '',
    prenom: '',
    Intitul_Offre: '',
    Nom_Entreprise: '',
    Domaine: ''
  };
  conditions: string[] = [
    "Avoir une connexion internet stable.",
    "Être dans un environnement calme.",
    "Avoir un micro fonctionnel.",
    "Répondre de façon honnête et spontanée."
  ];
  confirme: boolean = false;

  constructor(
    private titleService: Title,
    private ngZone: NgZone,
    private router: Router,
    private route: ActivatedRoute,
    private alertsService: AlertsService,
    private entretienService: EntretienService
  ) {
    this.initializeSpeechRecognition();
    this.setupOnlineStatusListener();
  }

  private setupOnlineStatusListener() {
    window.addEventListener('online', () => this.ngZone.run(() => {
      this.isOnline = true;
      this.alertsService.showSuccess('Connexion internet rétablie');
    }));
    window.addEventListener('offline', () => this.ngZone.run(() => {
      this.isOnline = false;
      this.alertsService.showError('Connexion internet perdue. Veuillez vérifier votre connexion.');
    }));
  }

  private initializeSpeechRecognition() {
    if (!('webkitSpeechRecognition' in window)) {
      this.errorMessage = 'La reconnaissance vocale n\'est pas supportée par votre navigateur.';
      this.alertsService.showError(this.errorMessage);
      return;
    }

    const { webkitSpeechRecognition }: IWindow = <IWindow><unknown>window;
    this.recognition = new webkitSpeechRecognition();
    this.recognition.lang = 'fr-FR';
    this.recognition.interimResults = false;
    this.recognition.continuous = true;

    this.recognition.onresult = (event: any) => {
      this.ngZone.run(() => {
        for (let i = event.resultIndex; i < event.results.length; ++i) {
          if (event.results[i].isFinal) {
            const result = event.results[i][0].transcript;
            this.partialTranscript += (this.partialTranscript ? ' ' : '') + result;
          }
        }
      });
    };

    this.recognition.onerror = (event: any) => {
      this.ngZone.run(() => {
        this.isListening = false;
        switch (event.error) {
          case 'no-speech':
            this.alertsService.showWarning('Aucune parole détectée. Veuillez parler plus fort ou vérifier votre microphone.');
            break;
          case 'audio-capture':
            this.alertsService.showError('Microphone non détecté. Veuillez vérifier vos paramètres audio.');
            break;
          case 'not-allowed':
            this.alertsService.showError('Accès au microphone refusé. Veuillez autoriser l\'accès dans les paramètres de votre navigateur.');
            break;
          default:
            this.alertsService.showError('Erreur de reconnaissance vocale. Veuillez réessayer.');
        }
      });
    };

    this.recognition.onend = () => {
      this.ngZone.run(() => {
        if (this.isListening && this.remainingTime > 0) {
          try {
            this.recognition.start();
          } catch (e) {
            this.alertsService.showError('Erreur lors de la reprise de l\'enregistrement.');
          }
        }
      });
    };
  }

  ngOnInit() {
    this.titleService.setTitle('Entretien');
    this.loadInterviewQuestions();
  }

  private async loadInterviewQuestions() {
    try {
      const token = this.route.snapshot.params['token'];
      if (!token) {
        // Mode test : utiliser des questions par défaut
        console.log('Mode test : pas de token, utilisation de questions par défaut');
        this.questions = [
          "Pouvez-vous vous présenter en quelques mots ?",
          "Quelles sont vos principales compétences ?",
          "Pourquoi souhaitez-vous rejoindre notre entreprise ?"
        ];
        this.totalQuestions = this.questions.length;
        this.aiQuestion = this.questions[0];
        return;
      }

      this.isProcessing = true;
      const response = await firstValueFrom(this.entretienService.getInterviewDetails(token));
      this.questions = response.questions;
      this.totalQuestions = this.questions.length;
      this.aiQuestion = this.questions[0];
      this.isProcessing = false;
    } catch (error) {
      this.isProcessing = false;
      this.alertsService.showError('Erreur lors du chargement des questions');
      this.router.navigate(['/dashboard']);
    }
  }

  async demarrerQuestionIA() {
    if (!this.isOnline) {
      this.alertsService.showError('Pas de connexion internet. Veuillez vérifier votre connexion.');
      return;
    }

    try {
      this.aiSpeaking = true;
      const synth = window.speechSynthesis;
      const utter = new SpeechSynthesisUtterance(this.aiQuestion);
      utter.lang = 'fr-FR';
      utter.rate = 1.0;
      utter.pitch = 1.0;

      utter.onend = () => {
        this.ngZone.run(() => {
          this.aiSpeaking = false;
          this.startRecording();
        });
      };

      utter.onerror = (event) => {
        this.ngZone.run(() => {
          this.aiSpeaking = false;
          this.alertsService.showError('Erreur lors de la lecture de la question');
        });
      };

      synth.speak(utter);
    } catch (error) {
      this.aiSpeaking = false;
      this.alertsService.showError('Erreur lors de la synthèse vocale');
    }
  }

  async submitAnswer() {
    if (!this.transcript.trim()) {
      this.alertsService.showWarning('Veuillez fournir une réponse avant de continuer.');
      return;
    }

    try {
      this.isProcessing = true;
      const token = this.route.snapshot.params['token'];

      if (token) {
        await firstValueFrom(this.entretienService.submitAnswer(token, this.currentQuestionIndex, this.transcript));
      } else {
        // Mode test : simuler l'envoi
        console.log(`Mode test - Réponse ${this.currentQuestionIndex + 1}: ${this.transcript}`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simuler délai API
      }
      
      this.currentQuestionIndex++;
      if (this.currentQuestionIndex < this.totalQuestions) {
        this.aiQuestion = this.questions[this.currentQuestionIndex];
        this.transcript = '';
        this.partialTranscript = '';
        this.demarrerQuestionIA();
      } else {
        this.alertsService.showSuccess('Entretien terminé !');
        this.router.navigate(['/dashboard']);
      }
    } catch (error) {
      this.alertsService.showError('Erreur lors de l\'envoi de la réponse');
    } finally {
      this.isProcessing = false;
    }
  }

  toggleListening() {
    if (!this.isListening) {
      this.startRecording();
    } else {
      this.stopRecording();
    }
  }

  startRecording() {
    this.isListening = true;
    this.transcript = '';
    this.remainingTime = this.maxRecordingTime;
    this.recognition.start();

    this.recognition.onend = () => {
      if (this.isListening && this.remainingTime > 0) {
        this.recognition.start(); // relancer si pas fini
      }
    };

    this.recordingTimer = setInterval(() => {
      this.remainingTime--;
      if (this.remainingTime <= 0) {
        this.stopRecording();
      }
    }, 1000);
  }

  stopRecording() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    this.isListening = false;
    this.recognition.onend = null;
    this.recognition.stop();
    this.transcript = this.partialTranscript.trim();
  }

  ngOnDestroy() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
    if (this.recognition) {
      this.recognition.stop();
    }
    window.speechSynthesis.cancel();
    window.removeEventListener('online', () => {});
    window.removeEventListener('offline', () => {});
  }

  asFormGroup(control: AbstractControl): FormGroup {
    return control as FormGroup;
  }

  commencerEntretien() {
    this.etape = 2;
  }

  confirmerConditions() {
    if (this.confirme) {
      this.etape = 3;
      this.demarrerQuestionIA();
    }
  }
}

interface IWindow extends Window {
  webkitSpeechRecognition: any;
}

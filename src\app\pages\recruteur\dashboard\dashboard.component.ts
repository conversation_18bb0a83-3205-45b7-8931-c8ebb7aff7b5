import { Component, OnInit, OnDestroy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { OffreService, Offre } from 'src/app/services/offre.service';
import { Subject } from 'rxjs';
import { takeUntil, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})

export class DashboardComponent implements OnInit, OnDestroy {
  // Propriétés pour la gestion des données
  offres: Offre[] = [];
  isLoading = false;
  currentUserId: number | null = null;
  private destroy$ = new Subject<void>();
  private nouvellesOffresIds: Set<number> = new Set(); // Pour tracker les nouvelles offres
  constructor(
    private titleService: Title,
    private authService: AuthService,
    private router: Router,
    private offreService: OffreService
  ) {
    // Récupérer l'ID de l'utilisateur connecté
    this.currentUserId = this.authService.getCurrentUserId();
  }

  ngOnInit(): void {
    this.titleService.setTitle('Recruteur Home');
    this.loadOffres();
    this.subscribeToOffresUpdates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== MÉTHODES POUR CHARGER LES DONNÉES =====

  // Charger les offres du recruteur connecté
  loadOffres(): void {
    if (!this.currentUserId) {
      console.error('Aucun utilisateur connecté');
      this.loadMockData(); // Fallback vers les données de test
      return;
    }

    this.isLoading = true;

    // Appel API pour récupérer les offres du recruteur
    this.offreService.getOffresByRecruteur(this.currentUserId)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Erreur lors du chargement des offres:', error);
          // En cas d'erreur (backend non disponible), utiliser les données de test
          this.loadMockData();
          return of([]);
        })
      )
      .subscribe({
        next: (offres) => {
          this.offres = offres;
          this.isLoading = false;
          // Mettre à jour la liste dans le service
          this.offreService.updateOffresList(offres);
          console.log('Offres chargées depuis l\'API:', offres);
        },
        error: (error) => {
          console.error('Erreur lors du chargement des offres:', error);
          this.isLoading = false;
          this.loadMockData();
        }
      });
  }

  // S'abonner aux mises à jour des offres
  private subscribeToOffresUpdates(): void {
    let previousCount = 0;

    // Écouter les changements dans la liste des offres
    this.offreService.offresList$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (offres) => {
          if (offres.length > 0) {
            const currentCount = offres.length;

            // Vérifier si une nouvelle offre a été ajoutée
            if (previousCount > 0 && currentCount > previousCount) {
              this.showNewOffreNotification();
              // Marquer la première offre comme nouvelle (elle est ajoutée en premier)
              if (offres.length > 0) {
                this.nouvellesOffresIds.add(offres[0].id);
                // Retirer le marquage après 3 secondes
                setTimeout(() => {
                  this.nouvellesOffresIds.delete(offres[0].id);
                }, 3000);
              }
            }

            this.offres = offres;
            previousCount = currentCount;
            console.log('Liste des offres mise à jour:', offres);
          }
        }
      });

    // Écouter les notifications de mise à jour
    this.offreService.offresUpdated$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updated) => {
          if (updated) {
            console.log('Notification de mise à jour des offres reçue');
            // Optionnel : recharger depuis l'API si nécessaire
            // this.loadOffres();
          }
        }
      });
  }

  // Afficher une notification pour une nouvelle offre
  private showNewOffreNotification(): void {
    Swal.fire({
      title: 'Nouvelle offre ajoutée !',
      text: 'Votre offre d\'emploi a été publiée avec succès.',
      icon: 'success',
      timer: 3000,
      timerProgressBar: true,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  }

  // Données de test (fallback quand le backend n'est pas disponible)
  private loadMockData(): void {
    console.log('Chargement des données de test (backend non disponible)');
    this.offres = [
      {
        id: 1,
        titre: 'Développeur Full-Stack',
        description: 'Nous recherchons un développeur full-stack expérimenté...',
        domaine: 'Developpeur FULL-STACK',
        ville: 'Tétouan',
        typeContrat: 'cdi',
        salaire: '15000 MAD',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-12-31'),
        competences: ['Angular', 'Node.js', 'MongoDB'],
        recruteurId: this.currentUserId || 1,
        expanded: false
      },
      {
        id: 2,
        titre: 'Marketing Manager',
        description: 'Poste de manager marketing pour diriger notre équipe...',
        domaine: 'Les Ressources Humaines',
        ville: 'Salé',
        typeContrat: 'cdi',
        salaire: '12000 MAD',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-12-31'),
        competences: ['Marketing digital', 'SEO', 'Analytics'],
        recruteurId: this.currentUserId || 1,
        expanded: false
      },
      {
        id: 3,
        titre: 'Data Scientist',
        description: 'Analyste de données pour nos projets d\'IA...',
        domaine: 'Developpeur BACK-END',
        ville: 'Béni Mellal',
        typeContrat: 'cdd',
        salaire: '18000 MAD',
        heuresParSemaine: 35,
        dateLimite: new Date('2024-12-31'),
        competences: ['Python', 'Machine Learning', 'SQL'],
        recruteurId: this.currentUserId || 1,
        expanded: false
      },
      {
        id: 4,
        titre: 'Designer UX/UI',
        description: 'Designer créatif pour nos interfaces utilisateur...',
        domaine: 'Désigner UI / UX',
        ville: 'Casablanca',
        typeContrat: 'freelance',
        salaire: '10000 MAD',
        heuresParSemaine: 30,
        dateLimite: new Date('2024-12-31'),
        competences: ['Figma', 'Adobe XD', 'Prototyping'],
        recruteurId: this.currentUserId || 1,
        expanded: false
      },
    ];
    this.isLoading = false;
    // Mettre à jour la liste dans le service
    this.offreService.updateOffresList(this.offres);
  }

  isProfileMenuOpen = false;
  toggleProfileMenu() {
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }
  closeProfileMenu() {
    this.isProfileMenuOpen = false;
  }
    deconnexion(): void {
    this.authService.logout();
  }

  logout(): void {
    this.authService.logout();
  }

  // Méthode pour supprimer une offre (dynamique avec API backend)
  deleteOffre(offreId: number): void {
    Swal.fire({
      title: 'Êtes-vous sûr ?',
      text: 'Cette action supprimera définitivement l\'offre !',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Oui, supprimer !',
      cancelButtonText: 'Annuler'
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        // Appel API backend pour supprimer l'offre
        this.offreService.deleteOffre(offreId)
          .pipe(
            takeUntil(this.destroy$),
            catchError(error => {
              console.error('Erreur lors de la suppression:', error);
              // En cas d'erreur, supprimer localement quand même
              this.offres = this.offres.filter(offre => offre.id !== offreId);
              this.isLoading = false;
              Swal.fire(
                'Supprimé !',
                'L\'offre a été supprimée localement (backend non disponible).',
                'warning'
              );
              return of(null);
            })
          )
          .subscribe({
            next: () => {
              // Suppression réussie, recharger les offres
              this.loadOffres();
              Swal.fire(
                'Supprimé !',
                'L\'offre a été supprimée avec succès.',
                'success'
              );
            },
            error: (error) => {
              console.error('Erreur lors de la suppression:', error);
              this.isLoading = false;
              Swal.fire(
                'Erreur !',
                'Une erreur est survenue lors de la suppression.',
                'error'
              );
            }
          });
      }
    });
  }

  // Méthode pour modifier une offre (redirection vers post-job avec données pré-remplies)
  editOffre(offre: Offre): void {
    // Récupérer les détails complets de l'offre depuis l'API
    this.offreService.getOffreById(offre.id)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Erreur lors de la récupération des détails:', error);
          // En cas d'erreur, utiliser les données disponibles
          return of(offre);
        })
      )
      .subscribe({
        next: (offreComplete) => {
          // Utiliser le service pour partager les données d'édition
          this.offreService.setOffreToEdit(offreComplete);

          // Navigation vers post-job
          this.router.navigate(['/recruteur/post-job']);
        },
        error: (error) => {
          console.error('Erreur lors de la récupération des détails:', error);
          // En cas d'erreur, utiliser les données disponibles
          this.offreService.setOffreToEdit(offre);
          this.router.navigate(['/recruteur/post-job']);
        }
      });
  }

  // Méthode pour rafraîchir les données
  refreshOffres(): void {
    console.log('Rafraîchissement des offres...');
    this.loadOffres();
  }

  // Méthode utilitaire pour afficher les compétences
  getCompetencesText(competences: string[] | undefined): string {
    if (!competences || competences.length === 0) {
      return 'Non spécifiées';
    }
    return competences.join(', ');
  }

  // Vérifier si une offre est nouvelle
  isNouvelleOffre(offreId: number): boolean {
    return this.nouvellesOffresIds.has(offreId);
  }

  // Méthode pour changer le statut d'une offre (Actif/Inactif)
  toggleOffreStatus(offre: Offre): void {
    const newStatus = offre.expanded ? 'Inactif' : 'Actif'; // Utilisation temporaire de 'expanded' comme statut

    this.offreService.changeOffreStatus(offre.id, newStatus)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Erreur lors du changement de statut:', error);
          // En cas d'erreur, changer localement
          offre.expanded = !offre.expanded;
          Swal.fire(
            'Statut modifié !',
            `L'offre est maintenant ${newStatus} (changement local)`,
            'warning'
          );
          return of(offre);
        })
      )
      .subscribe({
        next: (offreUpdated) => {
          // Mettre à jour l'offre dans la liste
          const index = this.offres.findIndex(o => o.id === offre.id);
          if (index !== -1) {
            this.offres[index] = offreUpdated;
          }
          Swal.fire(
            'Statut modifié !',
            `L'offre est maintenant ${newStatus}`,
            'success'
          );
        },
        error: (error) => {
          console.error('Erreur lors du changement de statut:', error);
          Swal.fire(
            'Erreur !',
            'Une erreur est survenue lors du changement de statut.',
            'error'
          );
        }
      });
  }

}

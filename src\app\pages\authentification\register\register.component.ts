import { group } from '@angular/animations';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ValidationErrors , ValidatorFn , AbstractControl} from '@angular/forms';
import { Router } from '@angular/router';
import { AlertsService } from 'src/app/services/alerts.service';
import { Title } from '@angular/platform-browser';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  firstFormGroup!: FormGroup;
  secondFormGroup!: FormGroup;
  profileFormGroup!: FormGroup;
  emailPattern: string = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
  hidePassword = true;
  hideConfirmPassword = true;

  // pattern_mot_passe: /^(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{7,}$/

  rules = {
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
    length: false
  };

  // ---------------------------------------------------------------------

  constructor(private _formBuilder: FormBuilder ,
    private router: Router ,
    private alertsService: AlertsService ,
    private titleService: Title,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.firstFormGroup = this._formBuilder.group({
      nom: ['', [Validators.required, Validators.pattern("^[A-Za-zÀ-ÖØ-öø-ÿ]{3,}$")]],
      prenom: ['', [Validators.required, Validators.pattern("^[A-Za-zÀ-ÖØ-öø-ÿ]{3,}$")]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      telephone: [
        '',
        [
          Validators.required,
          Validators.pattern(/^(06|07)[0-9]{8}$/) // Numéro marocain simple
        ]
      ]
    });

    this.secondFormGroup = this._formBuilder.group({
      password: ['', [Validators.required , 
        Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])(?=.{8,}).*$/)
      ]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.passwordsMatchValidator() });
    
    this.profileFormGroup = this._formBuilder.group({
      userType: ['', Validators.required],
      cv: [null]
    });
    
    this.profileFormGroup.get('userType')?.valueChanges.subscribe(type => {
      const cvControl = this.profileFormGroup.get('cv');
      if (type === 'candidate') {
        cvControl?.setValidators(Validators.required);
      } else {
        cvControl?.clearValidators();
      }
      cvControl?.updateValueAndValidity();
    });
    this.titleService.setTitle('Inscription');

  }
// Validator personnalisé
passwordsMatchValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const group = control as FormGroup;
    const password = group.get('password')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;

    return password === confirmPassword ? null : { passwordMismatch: true };
  };
}

  selectUserType(type: string): void {
    this.profileFormGroup.get('userType')?.setValue(type);
    if (type === 'recruiter') {
      // Réinitialiser le champ CV si on passe à Recruteur
      this.profileFormGroup.get('cv')?.reset();
    }
  
    if (type === 'candidate') {
      // Optionnel : tu peux aussi forcer le reset ici pour être sûr
      this.profileFormGroup.get('cv')?.reset();
    }
  }
  
  onCVSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.profileFormGroup.get('cv')?.setValue(file);
    }
  }
  
  removeCV(): void {
    this.profileFormGroup.get('cv')?.setValue(null);
  }  
  
  onSubmit() {
    // Collecter toutes les données des formulaires
    const firstFormData = this.firstFormGroup.value;
    const secondFormData = this.secondFormGroup.value;
    const profileFormData = this.profileFormGroup.value;

    const formData = new FormData();

    // Ajouter les champs texte de la première étape
    formData.append('nom', firstFormData.nom);
    formData.append('prenom', firstFormData.prenom);
    formData.append('email', firstFormData.email);
    formData.append('telephone', firstFormData.telephone);

    // Ajouter les champs texte de la deuxième étape
    formData.append('password', secondFormData.password);

    // Ajouter les champs de la troisième étape
    formData.append('userType', profileFormData.userType);

    // Ajouter le fichier CV si l'utilisateur est un candidat et qu'un CV a été sélectionné
    const userType = profileFormData.userType;
    const cvFile = profileFormData.cv;

    if (userType === 'candidate' && cvFile) {
      formData.append('cv', cvFile);
    }

    // Appeler le service d'authentification pour l'inscription
    this.authService.register(formData).subscribe({
      next: (res) => {
        this.alertsService.showSuccess('Inscription réussie !');
        // Rediriger après inscription réussie
        if (userType === 'recruiter') {
          this.router.navigate(['/recruteur/dashboard']);
        } else if (userType === 'candidate') {
          this.router.navigate(['/candidate/dashboard']);
        }
      },
      error: (err) => {
        console.error('Erreur lors de l\'inscription:', err);
        // Afficher un message d'erreur à l'utilisateur
        // Utilise AlertsService si err.error.message contient un message spécifique du backend
        const errorMessage = err.error?.message || 'Erreur lors de l\'inscription. Veuillez réessayer.';
        this.alertsService.showError(errorMessage);
      }
    });
  }

  enregistrer() {
    // Après avoir bien enregistré...
    this.alertsService.showSuccess('Vos données ont été enregistrées avec succès.');
  }

  perteConnexion() {
    // Quand l'utilisateur n'a plus internet...
    this.alertsService.showError('Connexion internet perdue. Veuillez réessayer plus tard.');
  }
  
  checkPassword() {
    const pwd = this.secondFormGroup.get('password')?.value || '';
    this.rules.uppercase = /[A-Z]/.test(pwd);
    this.rules.lowercase = /[a-z]/.test(pwd);
    this.rules.number = /[0-9]/.test(pwd);
    this.rules.special = /[^A-Za-z0-9]/.test(pwd);
    this.rules.length = pwd.length > 8;
  }

}


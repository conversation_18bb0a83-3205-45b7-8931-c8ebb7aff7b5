import { Component, OnInit } from '@angular/core';
import { StatsService } from '../../services/stats.service';
import { AuthService } from '../../services/auth.service';

interface CandidateStats {
  totalApplications: number;
  interviewsCompleted: number;
  interviewsPending: number;
  averageScore: number;
  lastApplicationDate: Date;
  topSkills: string[];
  applicationStatus: {
    accepted: number;
    rejected: number;
    pending: number;
  };
}

@Component({
  selector: 'app-candidate-stats',
  template: `
    <mat-card class="stats-card">
      <mat-card-header>
        <mat-card-title>Mes Statistiques</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="stats-grid">
          <!-- Applications -->
          <div class="stat-item">
            <mat-icon>assignment</mat-icon>
            <div class="stat-content">
              <h3>{{stats.totalApplications}}</h3>
              <p>Total des candidatures</p>
            </div>
          </div>

          <!-- Entretiens -->
          <div class="stat-item">
            <mat-icon>mic</mat-icon>
            <div class="stat-content">
              <h3>{{stats.interviewsCompleted}}</h3>
              <p>Entretiens complétés</p>
            </div>
          </div>

          <!-- Score moyen -->
          <div class="stat-item">
            <mat-icon>star</mat-icon>
            <div class="stat-content">
              <h3>{{stats.averageScore}}%</h3>
              <p>Score moyen</p>
            </div>
          </div>

          <!-- Statut des candidatures -->
          <div class="stat-item">
            <mat-icon>pie_chart</mat-icon>
            <div class="stat-content">
              <div class="status-grid">
                <div class="status-item accepted">
                  <span>{{stats.applicationStatus.accepted}}</span>
                  <p>Acceptées</p>
                </div>
                <div class="status-item rejected">
                  <span>{{stats.applicationStatus.rejected}}</span>
                  <p>Refusées</p>
                </div>
                <div class="status-item pending">
                  <span>{{stats.applicationStatus.pending}}</span>
                  <p>En attente</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Compétences principales -->
        <div class="skills-section">
          <h3>Compétences principales</h3>
          <div class="skills-list">
            <mat-chip *ngFor="let skill of stats.topSkills" color="primary" selected>
              {{skill}}
            </mat-chip>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .stats-card {
      margin: 20px;
      padding: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      padding: 15px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat-item mat-icon {
      font-size: 2em;
      margin-right: 15px;
      color: #3f51b5;
    }

    .stat-content h3 {
      margin: 0;
      font-size: 1.5em;
      color: #333;
    }

    .stat-content p {
      margin: 5px 0 0;
      color: #666;
    }

    .status-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }

    .status-item {
      text-align: center;
      padding: 10px;
      border-radius: 4px;
    }

    .status-item span {
      font-size: 1.2em;
      font-weight: bold;
    }

    .status-item.accepted {
      background: #e8f5e9;
      color: #2e7d32;
    }

    .status-item.rejected {
      background: #ffebee;
      color: #c62828;
    }

    .status-item.pending {
      background: #fff3e0;
      color: #ef6c00;
    }

    .skills-section {
      margin-top: 20px;
    }

    .skills-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .mat-chip {
      margin: 5px;
    }
  `]
})
export class CandidateStatsComponent implements OnInit {
  stats: CandidateStats = {
    totalApplications: 0,
    interviewsCompleted: 0,
    interviewsPending: 0,
    averageScore: 0,
    lastApplicationDate: new Date(),
    topSkills: [],
    applicationStatus: {
      accepted: 0,
      rejected: 0,
      pending: 0
    }
  };

  constructor(
    private statsService: StatsService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.loadStats();
  }

  private loadStats() {
    const userId = this.authService.getCurrentUserId();
    if (userId) {
      this.statsService.getCandidateStats(userId).subscribe({
        next: (data: CandidateStats) => {
          this.stats = data;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des statistiques:', error);
        }
      });
    }
  }
} 
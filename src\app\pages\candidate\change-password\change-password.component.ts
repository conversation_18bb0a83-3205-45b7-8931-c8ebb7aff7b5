import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  changePasswordForm!: FormGroup;
  isLoading = false;
  hideCurrentPassword = true;
  hideNewPassword = true;
  hideConfirmPassword = true;

  constructor(
    private fb: FormBuilder,
    private titleService: Title,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Changer le mot de passe');
    this.initForm();
  }

  // Initialiser le formulaire avec validations
  initForm(): void {
    this.changePasswordForm = this.fb.group({
      currentPassword: ['', [Validators.required, Validators.minLength(6)]],
      newPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        this.passwordStrengthValidator
      ]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  // Validateur personnalisé pour la force du mot de passe
  passwordStrengthValidator(control: AbstractControl): {[key: string]: any} | null {
    const value = control.value;
    if (!value) return null;

    const hasNumber = /[0-9]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasLower = /[a-z]/.test(value);
    const hasSpecial = /[#?!@$%^&*-]/.test(value);

    const valid = hasNumber && hasUpper && hasLower && hasSpecial;
    if (!valid) {
      return { passwordStrength: true };
    }
    return null;
  }

  // Validateur pour vérifier que les mots de passe correspondent
  passwordMatchValidator(group: AbstractControl): {[key: string]: any} | null {
    const newPassword = group.get('newPassword');
    const confirmPassword = group.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  // Obtenir les messages d'erreur pour chaque champ
  getErrorMessage(fieldName: string): string {
    const field = this.changePasswordForm.get(fieldName);

    if (field?.hasError('required')) {
      return 'Ce champ est requis';
    }

    if (fieldName === 'currentPassword' && field?.hasError('minlength')) {
      return 'Le mot de passe actuel doit contenir au moins 6 caractères';
    }

    if (fieldName === 'newPassword') {
      if (field?.hasError('minlength')) {
        return 'Le nouveau mot de passe doit contenir au moins 8 caractères';
      }
      // Le message de passwordStrength est affiché séparément
      if (field?.hasError('passwordStrength')) {
        return '';
      }
    }

    if (fieldName === 'confirmPassword' && this.changePasswordForm.hasError('passwordMismatch')) {
      return 'Les mots de passe ne correspondent pas';
    }

    return '';
  }

  // Vérifier si un champ a des erreurs
  hasError(fieldName: string): boolean {
    const field = this.changePasswordForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Soumettre le formulaire
  onSubmit(): void {
    if (this.changePasswordForm.valid) {
      this.isLoading = true;
      const formData = this.changePasswordForm.value;

      // TODO: Appel API pour changer le mot de passe
      // this.authService.changePassword(formData).subscribe(...)

      // Simulation d'appel API
      setTimeout(() => {
        this.isLoading = false;
        this.showSuccessMessage();
      }, 2000);

    } else {
      this.markFormGroupTouched();
    }
  }

  // Marquer tous les champs comme touchés pour afficher les erreurs
  private markFormGroupTouched(): void {
    Object.values(this.changePasswordForm.controls).forEach(control => {
      control.markAsTouched();
    });
  }

  // Afficher message de succès
  private showSuccessMessage(): void {
    Swal.fire({
      title: 'Succès !',
      text: 'Votre mot de passe a été modifié avec succès.',
      icon: 'success',
      confirmButtonText: 'OK'
    }).then(() => {
      // Rediriger vers le dashboard ou déconnecter l'utilisateur
      this.router.navigate(['/candidate/dashboard']);
    });
  }

  // Annuler et retourner au dashboard
  onCancel(): void {
    this.router.navigate(['/candidate/dashboard']);
  }
}

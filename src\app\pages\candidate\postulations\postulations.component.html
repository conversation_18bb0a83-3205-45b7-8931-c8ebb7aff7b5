<app-loading [isLoading]="isLoading" [message]="'Chargement de vos candidatures...'"></app-loading>

<div class="postulations-container">
  <div class="postulations-header">
    <h1>Mes candidatures</h1><br>
    <p>Suivez l'état de vos candidatures et accédez à vos entretiens</p>
  </div>

  <mat-card class="postulations-card">
    <mat-card-content>
      <table mat-table [dataSource]="postulations" class="postulations-table">
        <!-- Colonne Offre -->
        <ng-container matColumnDef="offre">
          <th mat-header-cell *matHeaderCellDef>Offre</th>
          <td mat-cell *matCellDef="let postulation">
            <div class="offre-info">
              <h3>{{ postulation.offre.titre }}</h3>
              <p>{{ postulation.offre.entreprise }} - {{ postulation.offre.ville }}</p>
              <p class="contrat-type">{{ postulation.offre.typeContrat }}</p>
            </div>
          </td>
        </ng-container>

        <!-- Colonne Date de postulation -->
        <ng-container matColumnDef="datePostulation">
          <th mat-header-cell *matHeaderCellDef>Date de candidature</th>
          <td mat-cell *matCellDef="let postulation">
            {{ postulation.datePostulation | date:'dd/MM/yyyy' }}
          </td>
        </ng-container>

        <!-- Colonne Score CV -->
        <ng-container matColumnDef="scoreCv">
          <th mat-header-cell *matHeaderCellDef>Score CV</th>
          <td mat-cell *matCellDef="let postulation">
            <div class="score-container">
              <mat-progress-bar
                mode="determinate"
                [value]="postulation.scoreCv"
                [color]="postulation.scoreCv >= 70 ? 'accent' : 'warn'">
              </mat-progress-bar>
              <span class="score-value">{{ postulation.scoreCv }}%</span>
            </div>
          </td>
        </ng-container>

        <!-- Colonne Décision CV -->
        <ng-container matColumnDef="decisionCv">
          <th mat-header-cell *matHeaderCellDef>Décision CV</th>
          <td mat-cell *matCellDef="let postulation">
            <mat-chip-list>
              <mat-chip [color]="getDecisionColor(postulation.decisionCv)" selected>
                {{ getDecisionLabel(postulation.decisionCv) }}
              </mat-chip>
            </mat-chip-list>
          </td>
        </ng-container>

        <!-- Colonne Score Entretien -->
        <ng-container matColumnDef="scoreEntretien">
          <th mat-header-cell *matHeaderCellDef>Score Entretien</th>
          <td mat-cell *matCellDef="let postulation">
            <div class="score-container" *ngIf="postulation.scoreEntretien">
              <mat-progress-bar
                mode="determinate"
                [value]="postulation.scoreEntretien"
                [color]="postulation.scoreEntretien >= 70 ? 'accent' : 'warn'">
              </mat-progress-bar>
              <span class="score-value">{{ postulation.scoreEntretien }}%</span>
            </div>
            <span *ngIf="!postulation.scoreEntretien">-</span>
          </td>
        </ng-container>

        <!-- Colonne Décision Entretien -->
        <ng-container matColumnDef="decisionEntretien">
          <th mat-header-cell *matHeaderCellDef>Décision Entretien</th>
          <td mat-cell *matCellDef="let postulation">
            <mat-chip-list *ngIf="postulation.decisionEntretien">
              <mat-chip [color]="getDecisionColor(postulation.decisionEntretien)" selected>
                {{ getDecisionLabel(postulation.decisionEntretien) }}
              </mat-chip>
            </mat-chip-list>
            <span *ngIf="!postulation.decisionEntretien">-</span>
          </td>
        </ng-container>

        <!-- Colonne Statut -->
        <ng-container matColumnDef="statut">
          <th mat-header-cell *matHeaderCellDef>Statut</th>
          <td mat-cell *matCellDef="let postulation">
            <mat-chip-list>
              <mat-chip [color]="getDecisionColor(postulation.statut)" selected>
                {{ getStatutLabel(postulation.statut) }}
              </mat-chip>
            </mat-chip-list>
          </td>
        </ng-container>

        <!-- Colonne Actions -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let postulation">
            <div class="actions-container">
              <!-- Bouton Voir l'offre -->
              <button mat-icon-button
                      matTooltip="Voir l'offre"
                      (click)="viewOffreDetails(postulation.offreId)">
                <mat-icon>visibility</mat-icon>
              </button>

              <!-- Bouton Entretien (si CV accepté) -->
              <button mat-icon-button
                      *ngIf="postulation.decisionCv === 'ACCEPTE' && postulation.entretienId"
                      matTooltip="Passer l'entretien"
                      (click)="startEntretien(postulation)">
                <mat-icon>mic</mat-icon>
              </button>

              <!-- Bouton Annuler (si en cours) -->
              <button mat-icon-button
                      *ngIf="postulation.statut === 'EN_COURS'"
                      matTooltip="Annuler la candidature"
                      (click)="cancelPostulation(postulation)">
                <mat-icon>cancel</mat-icon>
              </button>

              <!-- Boutons Confirmation disponibilité (si acceptée) -->
              <ng-container *ngIf="postulation.statut === 'ACCEPTEE' && !postulation.disponibiliteConfirmee">
                <button mat-icon-button
                        matTooltip="Confirmer disponibilité"
                        (click)="confirmDisponibilite(postulation, true)">
                  <mat-icon>check_circle</mat-icon>
                </button>
                <button mat-icon-button
                        matTooltip="Refuser"
                        (click)="confirmDisponibilite(postulation, false)">
                  <mat-icon>cancel</mat-icon>
                </button>
              </ng-container>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- Message si aucune postulation -->
      <div class="no-postulations" *ngIf="postulations.length === 0 && !isLoading">
        <mat-icon>work_off</mat-icon>
        <p>Vous n'avez pas encore de candidatures</p>
        <button mat-raised-button color="primary" routerLink="/candidate/dashboard">
          Voir les offres disponibles
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div> 
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container-t">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>
<div class="contact-container">
  <!-- Section avec image et informations -->
  <div class="container py-5">
    <div class="row align-items-center mb-5">
      <div class="col-md-6">
        <div class="contact-image-container">
          <img src="assets/images/contact_image.jpg"
               alt="Contactez notre équipe"
               class="contact-image img-fluid rounded shadow">
          <div class="image-overlay">
            <div class="overlay-content">
              <h3>L'équipe RecrutIQ vous attend</h3>
              <p>Découvrez comment RecrutIQ révolutionne le recrutement avec l'intelligence artificielle</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="contact-info">
          <h2 class="contact-main-title">Contactez RecrutIQ</h2>
          <p class="contact-description-text">
            L'équipe RecrutIQ est à votre disposition pour vous présenter notre solution
            révolutionnaire de recrutement par intelligence artificielle. Découvrez comment
            RecrutIQ peut transformer votre processus de recrutement et optimiser vos embauches.
          </p>
          <div class="contact-highlights">
            <div class="highlight-item">
              <i class="fas fa-clock contact-icon-color"></i>
              <span>Support RecrutIQ 24h/7j</span>
            </div>
            <div class="highlight-item">
              <i class="fas fa-users contact-icon-color"></i>
              <span>Experts RecrutIQ dédiés</span>
            </div>
            <div class="highlight-item">
              <i class="fas fa-shield-alt contact-icon-color"></i>
              <span>Plateforme RecrutIQ sécurisée</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section des méthodes de contact -->
    <div class="row">
      <div class="col-md-4" *ngFor="let method of contactMethods">
        <div class="contact-card text-center"
             [class.clickable]="method.link"
             (click)="handleContactClick(method)">
          <div class="contact-icon">
            <i [class]="method.icon"></i>
          </div>
          <h3 class="contact-title">{{ method.title }}</h3>
          <p class="contact-description">{{ method.description }}</p>
          <p class="contact-details">
            <a *ngIf="method.link; else plainText"
               [href]="method.link"
               class="contact-link">
              {{ method.details }}
            </a>
            <ng-template #plainText>
              {{ method.details }}
            </ng-template>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
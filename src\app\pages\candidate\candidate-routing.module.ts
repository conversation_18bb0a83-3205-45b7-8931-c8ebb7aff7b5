import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DashboardComponent } from './dashboard/dashboard.component';
import { ProfileComponent } from './profile/profile.component';
import { InterviewComponent } from './interview/interview.component';
import { JobListingComponent } from './job-listing/job-listing.component';
import { PostulationsComponent } from './postulations/postulations.component';
import { SavedOffersComponent } from './saved-offers/saved-offers.component';
import { CvComponent } from './cv/cv.component';
import { ChangePasswordComponent } from './change-password/change-password.component';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  { path: 'dashboard', component: DashboardComponent },
  { path: 'profile', component: ProfileComponent },
  { path: 'interview', component: InterviewComponent },
  { path: 'interview/:token', component: InterviewComponent },
  { path: 'job-listing', component: JobListingComponent },
  { path: 'postulations', component: PostulationsComponent },
  { path: 'saved-offers', component: SavedOffersComponent },
  { path: 'cv', component: CvComponent },
  { path: 'change-password', component: ChangePasswordComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CandidateRoutingModule { }

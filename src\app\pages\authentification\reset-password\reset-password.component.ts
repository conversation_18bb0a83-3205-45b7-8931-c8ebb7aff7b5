import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {
  resetForm!: FormGroup;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Réinitialiser le mot de passe');
    this.initForm();
  }

  // Initialiser le formulaire
  initForm(): void {
    this.resetForm = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.email
      ]]
    });
  }

  // Obtenir les messages d'erreur
  getErrorMessage(fieldName: string): string {
    const field = this.resetForm.get(fieldName);

    if (field?.hasError('required')) {
      return 'L\'adresse email est requise';
    }

    if (field?.hasError('email')) {
      return 'Veuillez entrer une adresse email valide';
    }

    return '';
  }

  // Vérifier si un champ a des erreurs
  hasError(fieldName: string): boolean {
    const field = this.resetForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Soumettre le formulaire
  onSubmit(): void {
    if (this.resetForm.valid) {
      this.isLoading = true;
      const email = this.resetForm.get('email')?.value;

      // TODO: Appel API pour envoyer l'email de réinitialisation
      // this.authService.sendResetPasswordEmail(email).subscribe({
      //   next: (response) => {
      //     console.log('Email de réinitialisation envoyé:', response);
      //     this.showSuccessMessage();
      //   },
      //   error: (error) => {
      //     console.error('Erreur lors de l\'envoi:', error);
      //     this.isLoading = false;
      //     this.showErrorMessage();
      //   }
      // });

      // Simulation d'appel API (2 secondes)
      setTimeout(() => {
        this.isLoading = false;
        this.showSuccessMessage();
      }, 2000);

    } else {
      this.markFormGroupTouched();
    }
  }

  // Marquer tous les champs comme touchés pour afficher les erreurs
  private markFormGroupTouched(): void {
    Object.values(this.resetForm.controls).forEach(control => {
      control.markAsTouched();
    });
  }

  // Afficher message de succès et rediriger
  private showSuccessMessage(): void {
    Swal.fire({
      title: 'Email envoyé !',
      text: 'Un email de réinitialisation a été envoyé à votre adresse. Vérifiez votre boîte de réception.',
      icon: 'success',
      confirmButtonText: 'Continuer',
      confirmButtonColor: '#2196f3'
    }).then(() => {
      // Redirection vers forgot-password
      this.router.navigate(['/forgot-password']);
    });
  }

  // Afficher message d'erreur
  private showErrorMessage(): void {
    Swal.fire({
      title: 'Erreur !',
      text: 'Une erreur est survenue lors de l\'envoi de l\'email. Veuillez réessayer.',
      icon: 'error',
      confirmButtonText: 'Réessayer',
      confirmButtonColor: '#f44336'
    });
  }

  // Retourner à la page de connexion
  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  // Aller vers l'inscription
  goToRegister(): void {
    this.router.navigate(['/register']);
  }
}

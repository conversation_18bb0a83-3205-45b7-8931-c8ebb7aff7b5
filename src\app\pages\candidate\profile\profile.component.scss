// Profile Component - Compatible avec background global
.profile-container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  

  .profile-header {
    text-align: center;
    margin-bottom: 3rem;
    background-color: transparent;
    border-radius: 12px;
    padding: 2rem;

    h1 {
      font-size: 2.5rem;
      color: #360e92;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    p {
      color: white;
      font-size: 1.2rem;
      opacity: 0.8;
    }
  }

  .profile-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;

    .profile-card {
      background-color: transparent;
      // backdrop-filter: blur(10px);
      padding: 2rem;
      border-radius: 12px;
      // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);

      ::ng-deep {
        .mat-card-header {
          margin-bottom: 1.5rem;
        }
        mat-card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: white !important; // Garder les titres de sections colorés
          }

        // Styles pour les champs de formulaire - TOUT EN BLANC
        .mat-form-field {
          .mat-form-field-label {
            color: white !important;
          }

          .mat-form-field-outline {
            color: white !important;
          }

          .mat-form-field-outline-thick {
            color: white !important;
          }

          .mat-input-element {
            background-color: transparent !important;
            color: white !important;
            caret-color: white;
            border: 1px solid white !important;
            border-radius: 4px;
            padding: 8px 12px;
          }

          .mat-select-value {
            background-color: transparent !important;
            color: white !important;
          }

          .mat-select-arrow {
            color: white !important;
          }

          textarea.mat-input-element {
            background-color: transparent !important;
            color: white !important;
            border: 1px solid white !important;
            border-radius: 4px;
            padding: 8px 12px;
          }

          // Style pour le conteneur de l'input
          .mat-form-field-flex {
            background-color: transparent !important;
            border-radius: 4px;
          }

          // Style pour les inputs en focus
          &.mat-focused {
            .mat-form-field-label {
              color: white !important;
            }

            .mat-input-element {
              background-color: transparent !important;
              border-color: white !important;
            }
          }

          // Style pour les placeholders
          .mat-input-element::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
          }
        }

        // Styles pour les boutons
        .mat-raised-button {
          &.mat-primary {
            background-color: #8b5cf6;
            color: white;
          }

          &.mat-accent {
            background-color: #06b6d4;
            color: white;
          }

          &.mat-warn {
            background-color: #ef4444;
            color: white;
          }
        }

        .mat-stroked-button {
          border-color: white;
          color: white;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }

        .mat-icon-button {
          color: rgba(255, 255, 255, 0.7);

          &.mat-warn {
            color: #ef4444;
          }
        }

        // Styles pour les expansion panels
        .mat-expansion-panel {
          background-color: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          margin-bottom: 0.5rem;
          box-shadow: none;

          .mat-expansion-panel-header {
            padding: 0 1rem;
            height: 56px;
            color: white;

            .mat-panel-title {
              font-weight: 500;
              color: #8b5cf6;
            }

            .mat-expansion-indicator::after {
              color: rgba(255, 255, 255, 0.7);
            }
          }

          .mat-expansion-panel-content {
            color: white;

            .mat-expansion-panel-body {
              padding: 1rem;
            }
          }

          .mat-action-row {
            justify-content: flex-end;
            padding: 0.5rem 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
          }
        }

        // Styles pour les erreurs
        .mat-error {
          color: #fca5a5 !important;
        }

        // Styles pour les icônes
        .mat-icon {
          color: white !important;
        }

        // Styles pour les datepickers
        .mat-datepicker-toggle {
          color: white !important;
        }

        // Styles pour les hints et autres textes
        .mat-hint {
          color: white !important;
        }

        // Styles pour tous les textes dans les form fields
        .mat-form-field-wrapper {
          color: white !important;
        }

        // Styles pour les selects - TOUT EN BLANC
        .mat-select-panel {
          background-color: rgba(0, 0, 0, 0.8) !important;
          backdrop-filter: blur(10px);
          border: 1px solid white;

          .mat-option {
            color: white !important;
            background-color: transparent;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
              color: white !important;
            }

            &.mat-selected {
              background-color: rgba(255, 255, 255, 0.2) !important;
              color: white !important;
            }
          }
        }
      }

      mat-card-content {
        .profile-picture-upload {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          margin-bottom: 2rem;

          img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #8b5cf6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .form-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 1rem;

          .full-width {
            grid-column: 1 / -1;
          }
        }

        .dynamic-item {
          display: flex;
          align-items: flex-start;
          gap: 0.5rem;
          margin-bottom: 0.5rem;

          mat-form-field {
            flex: 1;
          }

          button {
            margin-top: 8px;
          }
        }

        .dynamic-section {
          margin-bottom: 1rem;
        }

        button[color="accent"] {
          margin-top: 1rem;
          width: fit-content;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;

      button {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }
}

// Styles globaux pour forcer TOUT en blanc dans le profil
.profile-container {
  // Forcer tous les inputs à être blancs
  ::ng-deep {
    input, textarea, select {
      color: white !important;
      background-color: transparent !important;
      border-color: white !important;
    }

    // Tous les labels en blanc
    label, .mat-form-field-label, .mat-select-placeholder {
      color: white !important;
    }

    // Tous les textes en blanc sauf exceptions
    span, p, div {
      color: white !important;
    }

    // Exceptions pour les titres et boutons (garder leurs couleurs)
    h1, h2, h3, h4, h5, h6 {
      color: #8b5cf6 !important; // Titres en violet
    }

    button {
      // Les boutons gardent leurs couleurs d'origine
      color: inherit !important;
    }

    .mat-card-title {
      color: #8b5cf6 !important; // Titres de sections en violet
    }
  }
}

// Styles globaux pour les overlays Angular Material
::ng-deep {
  .mat-datepicker-content {
    background-color: rgba(30, 30, 30, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .mat-calendar {
      background-color: transparent;
      color: white;

      .mat-calendar-header {
        color: white;

        .mat-calendar-arrow {
          border-top-color: white;
        }
      }

      .mat-calendar-table {
        .mat-calendar-table-header {
          color: rgba(255, 255, 255, 0.7);
        }

        .mat-calendar-body-cell {
          color: white;

          &:hover {
            background-color: rgba(139, 92, 246, 0.2);
          }

          &.mat-calendar-body-selected {
            background-color: #8b5cf6;
            color: white;
          }

          &.mat-calendar-body-today:not(.mat-calendar-body-selected) {
            border-color: #8b5cf6;
          }
        }
      }
    }
  }

  .mat-select-panel {
    background-color: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: blur(10px);
    border: 1px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    .mat-option {
      color: white !important;
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
      }

      &.mat-selected {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 1rem;

    .profile-header {
      padding: 1rem;

      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .profile-form {
      .profile-card {
        padding: 1rem;

        ::ng-deep {
          .mat-card-header {
            margin-bottom: 1rem;

            .mat-card-title {
              font-size: 1.3rem;
            }
          }
        }

        mat-card-content {
          .form-grid {
            grid-template-columns: 1fr;
          }

          .dynamic-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;

            mat-form-field {
              width: 100%;
            }

            button {
              margin-top: 0.25rem;
              align-self: flex-end;
            }
          }
        }
      }

      .form-actions {
        flex-direction: column;

        button {
          width: 100%;
        }
      }
    }
  }
}
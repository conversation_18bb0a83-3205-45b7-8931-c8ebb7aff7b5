/* You can add global styles to this file, and also import other style files */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

// Variables globales
:root {
  --primary-color: #2196f3;
  --secondary-color: #1976d2;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --text-color: #333;
  --text-light: #666;
  --background-light: #f5f5f5;
  --border-color: #e0e0e0;
}

// Reset et styles de base
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
}

// ============================================================================
// BACKGROUND GLOBAL - IMAGES DE FOND CONDITIONNELLES
// ============================================================================

body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100vh;
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  font-family: Roboto, "Helvetica Neue", sans-serif;

  // Image par défaut pour tous les composants (sera changée dynamiquement)
  background-image: url('/assets/images/backgroundpic.png');
}

// Overlay assombri global pour backgroundpic.png uniquement (minimisé)
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  pointer-events: none;
  z-index: 0;
}

// Supprimer l'overlay pour la page d'accueil (qui utilise bg.png)
body.home-page-body::before {
  display: none !important;
}

// Layout principal
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  z-index: 1; // S'assurer que le contenu est au-dessus de l'overlay
}

.main-content {
  position: relative;
  z-index: 1;
  padding-top: 90px; /* Ajustez selon la hauteur de votre header */
  min-height: calc(100vh - 90px);
}

router-outlet + * {
  flex: 1;
  position: relative;
  z-index: 1; // S'assurer que le contenu des routes est au-dessus de l'overlay
}



// Styles pour les notifications
.success-snackbar {
  background-color: var(--success-color);
  color: white;

  .mat-simple-snackbar-action {
    color: white;
  }
}

.error-snackbar {
  background-color: var(--error-color);
  color: white;

  .mat-simple-snackbar-action {
    color: white;
  }
}

.info-snackbar {
  background-color: var(--primary-color);
  color: white;

  .mat-simple-snackbar-action {
    color: white;
  }
}

.warning-snackbar {
  background-color: var(--warning-color);
  color: white;

  .mat-simple-snackbar-action {
    color: white;
  }
}

// Styles pour les formulaires Material
.mat-form-field {
  width: 100%;
  margin-bottom: 1rem;

  &.mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: var(--border-color);
    }

    &:hover .mat-form-field-outline {
      color: var(--primary-color);
    }

    &.mat-focused .mat-form-field-outline {
      color: var(--primary-color);
    }
  }
}

// Styles pour les boutons Material
.mat-button,
.mat-raised-button {
  &.mat-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover {
      background-color: var(--secondary-color);
    }
  }
}

// Styles pour les cartes Material
.mat-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// Styles pour les dialogues Material
.mat-dialog-container {
  border-radius: 8px;
  padding: 1.5rem;

  .mat-dialog-title {
    margin: 0 0 1rem;
    font-size: 1.5rem;
    font-weight: 500;
  }

  .mat-dialog-content {
    margin: 0 0 1.5rem;
    padding: 0;
  }

  .mat-dialog-actions {
    margin: 0;
    padding: 0.5rem 0 0;
  }
}

// Styles pour les menus Material
.mat-menu-panel {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .mat-menu-item {
    &:hover {
      background-color: var(--background-light);
    }

    &.mat-primary {
      color: var(--primary-color);
    }
  }
}

// Styles pour les spinners Material
.mat-progress-spinner {
  circle {
    stroke: var(--primary-color);
  }
}

// Styles pour les toolbars Material
.mat-toolbar {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: var(--text-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Styles pour les tableaux Material
.mat-table {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  overflow: hidden;

  .mat-header-cell {
    background-color: rgba(245, 245, 245, 0.8);
    color: var(--text-color);
    font-weight: 500;
  }

  .mat-row {
    &:hover {
      background-color: rgba(245, 245, 245, 0.5);
    }
  }
}

// Styles pour les onglets Material
.mat-tab-group {
  .mat-tab-header {
    border-bottom: 1px solid var(--border-color);
  }

  .mat-tab-label {
    color: var(--text-color);
    opacity: 0.7;

    &.mat-tab-label-active {
      opacity: 1;
      color: var(--primary-color);
    }
  }

  .mat-ink-bar {
    background-color: var(--primary-color);
  }
}

// Styles pour les listes Material
.mat-list-item {
  &:hover {
    background-color: var(--background-light);
  }
}

// Styles pour les sélecteurs Material
.mat-select-panel {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .mat-option {
    &:hover {
      background-color: var(--background-light);
    }

    &.mat-selected {
      color: var(--primary-color);
    }
  }
}

// Styles pour les cases à cocher Material
.mat-checkbox {
  .mat-checkbox-frame {
    border-color: var(--border-color);
  }

  &.mat-checkbox-checked {
    .mat-checkbox-background {
      background-color: var(--primary-color);
    }
  }
}

// Styles pour les boutons radio Material
.mat-radio-button {
  .mat-radio-outer-circle {
    border-color: var(--border-color);
  }

  &.mat-radio-checked {
    .mat-radio-inner-circle {
      background-color: var(--primary-color);
    }

    .mat-radio-outer-circle {
      border-color: var(--primary-color);
    }
  }
}

// Styles pour les sliders Material
.mat-slider {
  .mat-slider-track-fill {
    background-color: var(--primary-color);
  }

  .mat-slider-thumb {
    background-color: var(--primary-color);
  }
}

// Styles pour les tooltips Material
.mat-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
}

// Styles pour les badges Material
.mat-badge-content {
  background-color: var(--primary-color);
  color: white;
}

// Styles pour les icônes Material
.mat-icon {
  &.mat-primary {
    color: var(--primary-color);
  }

  &.mat-accent {
    color: var(--secondary-color);
  }

  &.mat-warn {
    color: var(--error-color);
  }
}

// Styles pour les champs de recherche
.search-field {
  .mat-form-field-wrapper {
    margin: 0;
  }

  .mat-form-field-flex {
    background-color: white;
    border-radius: 8px;
    padding: 0.5rem 1rem;
  }

  .mat-form-field-underline {
    display: none;
  }

  .mat-form-field-prefix {
    color: var(--primary-color);
    margin-right: 0.5rem;
  }
}

// Styles pour les cartes d'offre
.offre-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  margin-bottom: 1rem;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .offre-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);

    h2 {
      margin: 0 0 0.5rem;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-color);
    }

    .offre-meta {
      color: var(--text-light);
      font-size: 0.875rem;

      span {
        margin-right: 1rem;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .offre-content {
    padding: 1.5rem;

    .offre-description {
      margin-bottom: 1.5rem;
      color: var(--text-color);
      line-height: 1.6;
    }

    .offre-competences {
      h3 {
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: 0.75rem;
        color: var(--text-color);
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        li {
          background-color: var(--background-light);
          color: var(--text-color);
          padding: 0.25rem 0.75rem;
          border-radius: 16px;
          font-size: 0.875rem;
        }
      }
    }
  }

  .offre-actions {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    button {
      min-width: 120px;
    }
  }
}

// Styles pour les formulaires de candidature
.postulation-form {
  .form-section {
    margin-bottom: 2rem;

    h3 {
      font-size: 1.1rem;
      font-weight: 500;
      margin-bottom: 1rem;
      color: var(--text-color);
    }
  }

  .cv-upload {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.3s ease;

    &:hover {
      border-color: var(--primary-color);
    }

    .upload-icon {
      font-size: 3rem;
      color: var(--text-light);
      margin-bottom: 1rem;
    }

    .upload-text {
      color: var(--text-light);
      margin-bottom: 1rem;
    }

    .file-hint {
      font-size: 0.875rem;
      color: var(--text-light);
    }
  }
}

// Styles pour les tableaux de bord
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .stat-card {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 0.875rem;
      color: var(--text-light);
      margin-bottom: 0.5rem;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 0.5rem;
    }

    .stat-change {
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;

      &.positive {
        color: var(--success-color);
      }

      &.negative {
        color: var(--error-color);
      }
    }
  }
}

// Styles pour les filtres
.filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h3 {
      font-size: 1.1rem;
      font-weight: 500;
      color: var(--text-color);
      margin: 0;
    }

    .filter-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

// Styles pour les notifications
.notification-badge {
  position: relative;
  display: inline-flex;

  .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--error-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 9px;
    padding: 0 6px;
  }
}

// Styles pour les profils
.profile-header {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 2rem;

  .profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: var(--background-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--text-light);
  }

  .profile-info {
    flex: 1;

    h1 {
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--text-color);
      margin: 0 0 0.5rem;
    }

    .profile-meta {
      color: var(--text-light);
      margin-bottom: 1rem;

      span {
        margin-right: 1.5rem;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .profile-actions {
      display: flex;
      gap: 1rem;
    }
  }
}

// Styles pour les entretiens
.entretien-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .entretien-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      font-size: 1.1rem;
      font-weight: 500;
      color: var(--text-color);
      margin: 0;
    }

    .entretien-status {
      font-size: 0.875rem;
      padding: 0.25rem 0.75rem;
      border-radius: 16px;
      font-weight: 500;

      &.scheduled {
        background-color: var(--primary-color);
        color: white;
      }

      &.completed {
        background-color: var(--success-color);
        color: white;
      }

      &.cancelled {
        background-color: var(--error-color);
        color: white;
      }
    }
  }

  .entretien-details {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 1rem;

    .detail-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .entretien-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }
}

// Styles pour les messages
.message-list {
  .message-item {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .sender-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .sender-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: var(--background-light);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;
          color: var(--text-light);
        }

        .sender-details {
          h4 {
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-color);
            margin: 0 0 0.25rem;
          }

          .message-meta {
            font-size: 0.875rem;
            color: var(--text-light);
          }
        }
      }

      .message-actions {
        display: flex;
        gap: 0.5rem;
      }
    }

    .message-content {
      color: var(--text-color);
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    .message-footer {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
    }
  }
}

// Styles pour les paramètres
.settings-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
    margin: 0 0 1.5rem;
  }

  .settings-form {
    max-width: 600px;
  }

  .settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
  }
}

// Styles pour les pages d'erreur
.error-page {
  text-align: center;
  padding: 4rem 2rem;

  .error-icon {
    font-size: 6rem;
    color: var(--error-color);
    margin-bottom: 2rem;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem;
  }

  p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin: 0 0 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .error-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }
}

// Styles pour les loaders
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;

    .spinner-text {
      color: var(--text-color);
      font-size: 1.1rem;
    }
  }
}

// Styles pour les tooltips personnalisés
.custom-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  max-width: 300px;
  word-wrap: break-word;
}

// Styles pour les animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

// Styles pour les media queries
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .filter-form {
    grid-template-columns: 1fr;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;

    .profile-info {
      .profile-meta {
        justify-content: center;
      }

      .profile-actions {
        justify-content: center;
      }
    }
  }

  .entretien-card {
    .entretien-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  .message-item {
    .message-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .message-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}

@media (max-width: 480px) {
  .offre-card {
    .offre-actions {
      flex-direction: column;

      button {
        width: 100%;
      }
    }
  }

  .error-page {
    padding: 2rem 1rem;

    h1 {
      font-size: 2rem;
    }

    .error-actions {
      flex-direction: column;

      button {
        width: 100%;
      }
    }
  }
}

// Styles pour les images
img {
  box-sizing: content-box;
}

/* Pour préserver les dimensions des images dans les conteneurs qui utilisent flexbox */
.d-flex img,
.flex-row img,
.flex-column img {
  flex-shrink: 0;
}

import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { SavedOffersService, SavedOffer } from '../../../services/saved-offers.service';
import { AuthService } from '../../../services/auth.service';
import { AlertsService } from '../../../services/alerts.service';
import { Router } from '@angular/router';
import { PostulationService } from '../../../services/postulation.service';

@Component({
  selector: 'app-saved-offers',
  templateUrl: './saved-offers.component.html',
  styleUrls: ['./saved-offers.component.scss']
})
export class SavedOffersComponent implements OnInit {
  savedOffers: SavedOffer[] = [];
  isLoading = false;

  constructor(
    private savedOffersService: SavedOffersService,
    private postulationService: PostulationService,
    private authService: AuthService,
    private alertsService: AlertsService,
    private router: Router,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Offres Sauvegardées');
    this.loadSavedOffers();
  }

  loadSavedOffers(): void {
    this.isLoading = true;
    const candidatId = this.authService.getCurrentUserId();
    
    if (candidatId) {
      this.savedOffersService.getSavedOffers(candidatId).subscribe({
        next: (offers) => {
          this.savedOffers = offers;
          this.isLoading = false;
        },
        error: (error) => {
          this.alertsService.showError('Erreur lors du chargement des offres sauvegardées');
          this.isLoading = false;
        }
      });
    } else {
      this.alertsService.showError('ID candidat non trouvé');
      this.isLoading = false;
    }
  }

  viewOfferDetails(offreId: number): void {
    this.router.navigate(['/offres', offreId]);
  }

  removeSavedOffer(savedOffer: SavedOffer): void {
    this.alertsService.showConfirm(
      'Confirmation',
      'Êtes-vous sûr de vouloir retirer cette offre de vos favoris ?',
      'Retirer',
      'Annuler'
    ).then((confirmed) => {
      if (confirmed) {
        this.savedOffersService.removeSavedOffer(savedOffer.id).subscribe({
          next: () => {
            this.alertsService.showSuccess('Offre retirée des favoris');
            this.loadSavedOffers();
          },
          error: (error) => {
            this.alertsService.showError('Erreur lors du retrait de l\'offre');
          }
        });
      }
    });
  }

  postuler(savedOffer: SavedOffer): void {
    const candidatId = this.authService.getCurrentUserId();
    if (candidatId) {
      this.postulationService.createPostulation(savedOffer.offreId, candidatId).subscribe({
        next: (postulation) => {
          this.alertsService.showSuccess('Candidature envoyée avec succès');
          // Retirer l'offre des favoris après postulation
          this.removeSavedOffer(savedOffer);
        },
        error: (error) => {
          this.alertsService.showError('Erreur lors de l\'envoi de la candidature');
        }
      });
    }
  }

  isOfferExpired(dateLimite: Date): boolean {
    return new Date(dateLimite) < new Date();
  }

  getDaysRemaining(dateLimite: Date): number {
    const today = new Date();
    const limit = new Date(dateLimite);
    const diffTime = limit.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
} 
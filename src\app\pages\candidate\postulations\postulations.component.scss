// Postulations Component - Compatible avec background global
.postulations-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  min-height: calc(100vh - 80px);
  background-color: transparent;
}

.postulations-header {
  text-align: center;
  margin-bottom: 3rem;
  background-color: transparent;
  // backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  h1 {
    color: violet;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 600;
  }

  p {
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
  }
}

.postulations-card {
  margin-bottom: 2rem;
  background-color:transparent;
  // backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.postulations-table {
  width: 100%;
  background-color: transparent;

  .mat-header-cell {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    background-color: transparent;
  }

  .mat-cell {
    color: var(--text-color);
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .mat-row {
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(25, 118, 210, 0.05);
    }
  }
}

.offre-info {
  h3 {
    margin: 0;
    color: white;
    font-size: 1rem;
  }

  p {
    margin: 0.25rem 0;
    color: white;
    font-size: 0.9rem;
  }

  .contrat-type {
    color: white;
    font-weight: 500;
  }
}

.score-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 150px;

  mat-progress-bar {
    flex: 1;
  }

  .score-value {
    min-width: 45px;
    text-align: right;
    font-weight: 500;
  }
}

.actions-container {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;

  button {
    &:hover {
      background-color: rgba(0,0,0,0.04);
    }
  }
}

.no-postulations {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 3rem 1rem;
  background-color: transparent;
  // backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;

  mat-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
  }

  p {
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  button {
    min-width: 200px;
    background-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--secondary-color) !important;
      transform: translateY(-1px);
    }
  }
}

// Styles pour les chips
::ng-deep {
  .mat-chip {
    &.mat-accent {
      background-color: #2ecc71;
      color: white;
    }

    &.mat-warn {
      background-color: #e74c3c;
      color: white;
    }

    &.mat-primary {
      background-color: #3498db;
      color: white;
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .postulations-table {
    .mat-header-cell,
    .mat-cell {
      padding: 0.5rem;
    }
  }

  .score-container {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .postulations-container {
    margin: 1rem auto;
  }

  .postulations-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;

    .mat-header-cell,
    .mat-cell {
      padding: 0.5rem;
    }
  }

  .actions-container {
    flex-wrap: wrap;
    justify-content: center;
  }
} 
th{
  color:white;
}
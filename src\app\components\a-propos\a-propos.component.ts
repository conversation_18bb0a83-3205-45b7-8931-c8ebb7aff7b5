import { Component , OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-a-propos',
  templateUrl: './a-propos.component.html',
  styleUrls: ['./a-propos.component.scss']
})
export class AProposComponent implements OnInit{
  constructor(private titleService: Title){}
  ngOnInit() {
    this.titleService.setTitle('A propos');
  }

  socialPlatforms = [
    {
      name: 'Facebook',
      icon: 'fab fa-facebook-f',
      description: 'Suivez RecrutIQ sur Facebook pour découvrir nos dernières innovations en intelligence artificielle appliquée au recrutement et nos success stories.',
      url: 'https://m.facebook.com/100074795379212/'
    },
    {
      name: 'X (Twitter)',
      icon: 'fab fa-x-twitter',
      description: 'Rejoignez la communauté RecrutIQ sur X pour des insights quotidiens sur l\'avenir du recrutement et les tendances RH alimentées par l\'IA.',
      url: 'https://x.com/home'
    },
    {
      name: 'YouTube',
      icon: 'fab fa-youtube',
      description: 'Découvrez nos tutoriels RecrutIQ, témoignages clients et démonstrations de notre plateforme IA sur notre chaîne YouTube officielle.',
      url: 'https://youtube.com/@foreverything-kd8ew?si=oTx1PX-cUC0tllH6'
    }
  ];

  // Méthode pour ouvrir les liens des réseaux sociaux
  openSocialLink(platform: any): void {
    if (platform.url) {
      window.open(platform.url, '_blank');
    }
  }
}

import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from 'src/app/services/auth.service';
import { AlertsService } from 'src/app/services/alerts.service';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  loginForm: FormGroup;
  showPassword: boolean = false;
  emailFocused: boolean = false;
  passwordFocused: boolean = false;
  isLoading = false;
  hasPasswordValue: boolean = false; // Variable simple pour contrôler l'affichage

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private titleService: Title,
    private authService: AuthService,
    private alertsService: AlertsService
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {
    this.titleService.setTitle('Connexion');
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      const { email, password } = this.loginForm.value;
      
      this.authService.login(email, password).subscribe({
        next: (res) => {
          // Supposons que la réponse contient le token et potentiellement les infos utilisateur
          // Stockage du token géré dans le service
          
          // Décoder le token pour obtenir le rôle (si le rôle est dans le token)
          const token = res.token || localStorage.getItem('token');
          let userRole = null;
          if (token) {
             try {
              // Assurez-vous que JwtHelperService est injecté ou accessible ici si besoin
              // Pour l'exemple, on décode manuellement ou via le service s'il a une méthode pour ça
              // Supposons que le service AuthService a une méthode pour décoder ou qu'on le fait ici
              // const decodedToken = this.authService.decodeToken(token); // si la méthode existe
              // userRole = decodedToken?.role; // Adapte selon la structure de ton token

              // Alternative simple si le backend renvoie le rôle directement dans la réponse de login:
               userRole = res.role; // <=== ** VÉRIFIE LA STRUCTURE DE LA RÉPONSE BACKEND **

             } catch (e) {
               console.error('Erreur de décodage du token', e);
               // Gérer l'erreur de token
               this.router.navigate(['/login']); // Rediriger vers login si token invalide
               return;
             }
          }

          // Rediriger selon le rôle
          if (userRole === 'CANDIDAT') {
            this.router.navigate(['/candidate/dashboard']);
          } else if (userRole === 'RECRUTEUR') {
            this.router.navigate(['/recruteur/dashboard']);
          } else {
            // Rôle inconnu ou non spécifié, rediriger vers une page par défaut
            this.router.navigate(['/']);
          }
        },
        error: (err) => {
          // Afficher une erreur à l'utilisateur (ex: identifiants invalides)
          console.error('Erreur de connexion:', err);
          this.alertsService.showError('Erreur lors de la connexion');
        }
      });
    } else {
      // Marquez tous les champs comme touchés pour afficher les erreurs de validation du formulaire
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }

  setFieldFocus(field: 'email' | 'password', isFocused: boolean): void {
    if (field === 'email') {
      this.emailFocused = isFocused;
    } else if (field === 'password') {
      this.passwordFocused = isFocused;
    }
  }

  // Méthode appelée à chaque saisie dans le champ password
  onPasswordInput(event: any): void {
    const value = event.target.value;
    this.hasPasswordValue = value && value.trim().length > 0;
    console.log('Password input:', value, 'hasPasswordValue:', this.hasPasswordValue); // Debug temporaire
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  navigateToRegister(): void {
    this.router.navigate(['authentification/register']);
  }

  navigateToForgotPassword(): void {
    this.router.navigate(['authentification/forgot-password']);
  }

  loginWithGoogle() {
    console.log('Login with Google');
    // Implement Google authentication logic
  }

  loginWithMicrosoft() {
    console.log('Login with Microsoft');
    // TODO: Intégrer l'authentification Microsoft (via Firebase, MSAL ou OAuth2)
  }

  loginWithFacebook() {
    console.log('Login with Facebook');
    // Implement Facebook authentication logic
  }
}

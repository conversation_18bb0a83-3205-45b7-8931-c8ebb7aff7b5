// .home-container {
//   height: 82vh;
//   background-size: cover;
//   background-position: center;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   text-align: center;
//   padding: 0 1rem;
// }
// .main-title {
//   font-size: 3.5rem;
//   color: #f0f0f0;
//   font-family: 'Poppins', sans-serif;
//   font-weight: 600;
//   letter-spacing: 1.5px;
//   text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
//   line-height: 1.2;
// }
.hero {
  position: relative;
  width: 100%;
  height: calc(100vh - 140px); /* laisse de la place pour le footer */
  background-color:transparent;
  background-size: cover; /* adapte l’image à l’écran */
  background-position: center;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0; /* hauteur minimale pour petits écrans */
}

/* Conteneur principal du texte et des boutons */
.hero-content {
  /* Permet de centrer le contenu au milieu de l’écran */
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  left: 5%; /* par exemple */
  transform: translateY(-50%); /* au lieu d’un translate(-50%, -50%) */
  text-align: left;
  /* Alignement du texte */
  text-align: center;
  /* Couleur blanche par défaut (sur fond sombre) */
  color: #fff;

  h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
  }
  }

// @media (max-width: 768px) {
//   .main-title {
//     font-size: 2rem;
//   }
// }

//   @keyframes typing {
//     from { width: 0 }
//     to { width: 100% }
//   }
  
//   @keyframes blink-caret {
//     from, to { border-color: transparent }
//     50% { border-color: #fff }
//   }
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface SavedOffer {
  id: number;
  candidatId: number;
  offreId: number;
  dateSauvegarde: Date;
  offre: {
    id: number;
    titre: string;
    entreprise: string;
    ville: string;
    typeContrat: string;
    dateLimite: Date;
    description: string;
    competences: string[];
    salaire?: string;
    avantages?: string[];
  };
}

@Injectable({
  providedIn: 'root'
})
export class SavedOffersService {
  private apiUrl = `${environment.apiUrl}/saved-offers`;

  constructor(private http: HttpClient) {}

  // Récupérer toutes les offres sauvegardées d'un candidat
  getSavedOffers(candidatId: number): Observable<SavedOffer[]> {
    return this.http.get<SavedOffer[]>(`${this.apiUrl}/candidat/${candidatId}`);
  }

  // Sauvegarder une offre
  saveOffer(offreId: number, candidatId: number): Observable<SavedOffer> {
    return this.http.post<SavedOffer>(`${this.apiUrl}`, {
      offreId,
      candidatId
    });
  }

  // Supprimer une offre sauvegardée
  removeSavedOffer(savedOfferId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${savedOfferId}`);
  }

  // Vérifier si une offre est déjà sauvegardée
  isOfferSaved(offreId: number, candidatId: number): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/check/${offreId}/${candidatId}`);
  }
} 
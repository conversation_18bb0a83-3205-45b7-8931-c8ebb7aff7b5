.reset-container {
    max-width: 400px;
    margin: 4rem auto;
    padding: 2rem;
    text-align: center;
    background: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
    .logo {
        width: 140px; // ajuste la taille si besoin
        height: auto;
        margin: 0 0;
      }
  
    h2 {
      margin-bottom: 1.5rem;
      color: aliceblue;
    }
  
    mat-form-field {
      width: 100%;
      margin-bottom: 1rem;

      &.mat-form-field-appearance-outline {
        // Bordures par défaut (état normal)
        .mat-form-field-outline {
          border-color: rgba(255, 255, 255, 0.5) !important;
          border-width: 1px;
        }

        .mat-form-field-outline-thick {
          border-color: rgba(255, 255, 255, 0.5) !important;
          border-width: 2px;
        }

        // État focus (quand on clique sur l'input) - FORCER BLANC
        &.mat-focused {
          .mat-form-field-outline-thick {
            color: white !important;
            border-color: white !important;
            border-width: 2px !important;
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
          }

          .mat-form-field-outline-start,
          .mat-form-field-outline-end,
          .mat-form-field-outline-gap {
            border-color: white !important;
          }

          // Label qui remonte (couleur blanche)
          .mat-form-field-label {
            color: white !important;
            font-weight: 500;
          }
        }

        // État hover (survol)
        &:hover:not(.mat-focused) {
          .mat-form-field-outline {
            border-color: rgba(255, 255, 255, 0.8) !important;
          }
        }

        // Quand le champ a une valeur (label reste en haut)
        &.mat-form-field-should-float {
          .mat-form-field-label {
            color: rgba(255, 255, 255, 0.9) !important;
          }
        }
      }

      // Couleur du texte saisi
      .mat-input-element {
        color: white !important;
        caret-color: white !important;
        font-weight: 400;
      }

      // Placeholder (quand pas de label ou en complément)
      .mat-input-element::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
        opacity: 1;
      }

      // Label par défaut (position basse)
      .mat-form-field-label {
        color: rgba(255, 255, 255, 0.8) !important;
        font-weight: 400;
      }

      // Icon color
      .mat-icon {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }
  
    button[type="submit"] {
      margin-top: 1rem;
      width: 100%;
    }
  }

  // !--------------------------------------------------------------------------

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}
  
.contact-container {
  .contact-header {
    padding: 60px 0;
    position: relative;
    // background-color: #1a3b5d;
    background-image: url('../../../assets/images/bg_ai.png') no-repeat center center fixed;
    background-size: cover;
    background-position: center;
    color: white;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      
      z-index: 0;
    }
    
    .container {
      position: relative;
      z-index: 1;
    }
    
    .text-brand {
      color: #ff5f45;
      font-weight: 700;
    }
    
    .underline-dots {
      width: 50px;
      height: 5px;
      margin-top: 15px;
      border-top: 2px dotted #ff5f45;
    }
    
    .lead {
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  // Section avec image et informations
  .contact-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    .contact-image {
      width: 100%;
      height: auto;
      max-height: 400px;
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      color: white;
      padding: 2rem;
      transform: translateY(100%);
      transition: transform 0.3s ease;

      .overlay-content {
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          opacity: 0.9;
        }
      }
    }

    &:hover .image-overlay {
      transform: translateY(0);
    }
  }

  .contact-info {
    padding-left: 2rem;

    .contact-main-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1.5rem;
      background: linear-gradient(45deg, #ff5f45, #ff8a65);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .contact-description-text {
      font-size: 1.1rem;
      line-height: 1.8;
      color: white;
      margin-bottom: 2rem;
      text-align: justify;
    }

    .contact-highlights {
      .highlight-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;

        i {
          font-size: 1.2rem;
          margin-right: 1rem;
          width: 20px;

          &.contact-icon-color {
            color: #ff5f45;
          }
        }

        span {
          font-weight: 500;
          color: whitesmoke;
        }
      }
    }
  }

  // Responsive pour la section image
  @media (max-width: 768px) {
    .contact-info {
      padding-left: 0;
      margin-top: 2rem;
      text-align: center;

      .contact-main-title {
        font-size: 2rem;
      }

      .contact-description-text {
        text-align: left;
      }

      .contact-highlights {
        .highlight-item {
          justify-content: center;
        }
      }
    }
  }

  .contact-card {
    padding: 30px 20px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    
    .contact-icon {
      margin-bottom: 20px;
      
      i {
        font-size: 40px;
        color: #ff5f45;
      }
    }
    
    .contact-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #ff5f45;
    }
    
    .contact-description {
      color: #6c757d;
      margin-bottom: 15px;
    }
    
    .contact-details {
      font-weight: 500;
      color: #ff5f45;
    }
    
    &:hover {
      background-color: white;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transform: translateY(-5px);
    }

    &.clickable {
      cursor: pointer;

      &:hover {
        background-color: #f8f9fa;
        transform: translateY(-8px);
      }
    }
  }

  .contact-link {
    color: #ff5f45;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      color: #e54a32;
      text-decoration: underline;
    }
  }
}

@media (max-width: 768px) {
  .contact-container {
    .contact-header {
      padding: 40px 0;
    }
  }
}
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container-t {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}
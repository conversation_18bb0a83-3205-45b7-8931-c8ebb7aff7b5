import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface PostulationRequest {
  offreId: number;
  lettreMotivation: string;
  cv: File;
}

export interface PostulationResponse {
  id: number;
  candidatId: number;
  offreId: number;
  datePostulation: Date;
  statut: 'EN_ATTENTE' | 'ACCEPTEE' | 'REFUSEE';
  scoreCv?: number;
  feedbackCv?: string;
  scoreEntretien?: number;
  feedbackEntretien?: string;
  dateEntretien?: Date;
  lienEntretien?: string;
  dateReponse?: Date;
  reponseCandidat?: 'OUI' | 'NON';
}

export interface Postulation {
  id: number;
  candidatId: number;
  offreId: number;
  datePostulation: Date;
  scoreCv: number;
  feedbackCv: string;
  decisionCv: 'ACCEPTE' | 'REFUSE' | 'EN_ATTENTE';
  entretienId?: number;
  scoreEntretien?: number;
  feedbackEntretien?: string;
  decisionEntretien?: 'ACCEPTE' | 'REFUSE' | 'EN_ATTENTE';
  statut: 'EN_COURS' | 'ACCEPTEE' | 'REFUSEE';
  offre: {
    id: number;
    titre: string;
    entreprise: string;
    ville: string;
    typeContrat: string;
    dateLimite: Date;
  };
}

@Injectable({
  providedIn: 'root'
})
export class PostulationService {
  private apiUrl = `${environment.apiUrl}/postulations`;

  constructor(private http: HttpClient) {}

  // Créer une nouvelle candidature
  postuler(request: PostulationRequest): Observable<PostulationResponse> {
    const formData = new FormData();
    formData.append('offreId', request.offreId.toString());
    formData.append('lettreMotivation', request.lettreMotivation);
    formData.append('cv', request.cv);

    return this.http.post<PostulationResponse>(this.apiUrl, formData);
  }

  // Récupérer toutes les candidatures du candidat
  getMesPostulations(): Observable<PostulationResponse[]> {
    return this.http.get<PostulationResponse[]>(`${this.apiUrl}/mes-postulations`);
  }

  // Récupérer une candidature par son ID
  getPostulationById(id: number): Observable<PostulationResponse> {
    return this.http.get<PostulationResponse>(`${this.apiUrl}/${id}`);
  }

  // Récupérer les candidatures pour une offre (recruteur)
  getPostulationsByOffre(offreId: number): Observable<PostulationResponse[]> {
    return this.http.get<PostulationResponse[]>(`${this.apiUrl}/offre/${offreId}`);
  }

  // Accepter une candidature (recruteur)
  accepterCandidature(postulationId: number): Observable<PostulationResponse> {
    return this.http.post<PostulationResponse>(`${this.apiUrl}/${postulationId}/accepter`, {});
  }

  // Refuser une candidature (recruteur)
  refuserCandidature(postulationId: number, motif: string): Observable<PostulationResponse> {
    return this.http.post<PostulationResponse>(`${this.apiUrl}/${postulationId}/refuser`, { motif });
  }

  // Répondre à une offre acceptée (candidat)
  repondreOffre(postulationId: number, reponse: 'OUI' | 'NON'): Observable<PostulationResponse> {
    return this.http.post<PostulationResponse>(`${this.apiUrl}/${postulationId}/repondre`, { reponse });
  }

  // Annuler une candidature (candidat)
  annulerPostulation(postulationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${postulationId}`);
  }

  // Récupérer les statistiques des candidatures (recruteur)
  getStatistiquesPostulations(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/statistiques`);
  }

  // Récupérer toutes les postulations d'un candidat
  getPostulationsByCandidat(candidatId: number): Observable<Postulation[]> {
    return this.http.get<Postulation[]>(`${this.apiUrl}/candidat/${candidatId}`);
  }

  // Créer une nouvelle postulation
  createPostulation(offreId: number, candidatId: number): Observable<Postulation> {
    return this.http.post<Postulation>(`${this.apiUrl}`, {
      offreId,
      candidatId
    });
  }

  // Récupérer les détails d'une postulation
  getPostulationDetails(postulationId: number): Observable<Postulation> {
    return this.http.get<Postulation>(`${this.apiUrl}/${postulationId}`);
  }

  // Annuler une postulation
  cancelPostulation(postulationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${postulationId}`);
  }

  // Confirmer la disponibilité après acceptation par le recruteur
  confirmDisponibilite(postulationId: number, disponible: boolean): Observable<Postulation> {
    return this.http.post<Postulation>(`${this.apiUrl}/${postulationId}/confirmation`, {
      disponible
    });
  }

  // Récupérer le lien d'entretien si la postulation est acceptée
  getEntretienLink(postulationId: number): Observable<{ lienEntretien: string }> {
    return this.http.get<{ lienEntretien: string }>(`${this.apiUrl}/${postulationId}/entretien-link`);
  }
} 
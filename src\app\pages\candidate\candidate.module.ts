import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// Routing Module
import { CandidateRoutingModule } from './candidate-routing.module';

// Material Module
import { MaterialModule } from '../../material.module';

// Components
import { ProfileComponent } from './profile/profile.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { InterviewComponent } from './interview/interview.component';
import { JobListingComponent } from './job-listing/job-listing.component';
import { PostulationsComponent } from './postulations/postulations.component';
import { SavedOffersComponent } from './saved-offers/saved-offers.component';
import { CvComponent } from './cv/cv.component';
import { ChangePasswordComponent } from './change-password/change-password.component';

const COMPONENTS = [
  ProfileComponent,
  DashboardComponent,
  InterviewComponent,
  JobListingComponent,
  PostulationsComponent,
  SavedOffersComponent,
  CvComponent
];

const MODULES = [
  CommonModule,
  FormsModule,
  ReactiveFormsModule,
  RouterModule,
  CandidateRoutingModule,
  MaterialModule
];

@NgModule({
  declarations: [...COMPONENTS, ChangePasswordComponent],
  imports: [...MODULES],
  exports: [...COMPONENTS, ...MODULES],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CandidateModule { } 
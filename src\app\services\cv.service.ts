import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CvInfo {
  id: number;
  candidatId: number;
  nomFichier: string;
  cheminFichier: string; // URL ou chemin d'accès au fichier
  dateUpload: string; // Format ISO 8601 YYYY-MM-DDTHH:mm:ss
  taille: number; // en octets
  texteExtrait: string; // Contenu textuel extrait par Apache Tika
}

@Injectable({
  providedIn: 'root'
})
export class CvService {
  private apiUrl = `${environment.apiUrl}/cv`;

  constructor(private http: HttpClient) { }

  uploadCv(file: File): Observable<CvInfo> {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post<CvInfo>(`${this.apiUrl}/upload`, formData);
  }

  getCvInfo(candidatId: number): Observable<CvInfo[]> {
    // Assuming a candidate can have multiple CVs or we fetch the most recent one.
    // Adjust API endpoint as per backend implementation (e.g., /cv/candidat/{id})
    return this.http.get<CvInfo[]>(`${this.apiUrl}/candidat/${candidatId}`);
  }

  deleteCv(cvId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${cvId}`);
  }

  getCvAnalysis(cvId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${cvId}/analysis`);
  }

  getCvScore(cvId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${cvId}/score`);
  }

  getCvFeedback(cvId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${cvId}/feedback`);
  }

  validateFile(file: File): boolean {
    // Vérification du type de fichier
    if (!environment.supportedFileTypes.includes(file.type)) {
      return false;
    }

    // Vérification de la taille
    if (file.size > environment.maxFileSize) {
      return false;
    }

    return true;
  }
} 
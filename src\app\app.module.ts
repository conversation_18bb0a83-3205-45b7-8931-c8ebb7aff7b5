import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { JwtModule } from '@auth0/angular-jwt';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { HomeComponent } from './pages/home/<USER>';
import { LoginComponent } from './pages/authentification/login/login.component';
import { RegisterComponent } from './pages/authentification/register/register.component';
import { ForgotPasswordComponent } from './pages/authentification/forgot-password/forgot-password.component';
import { JobCardComponent } from './pages/candidate/job-listing/job-card/job-card.component';
import { ContactComponent } from './components/contact/contact.component';
import { AProposComponent } from './components/a-propos/a-propos.component';
import { PostulationDialogComponent } from './components/postulation-dialog/postulation-dialog.component';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
import { LoadingComponent } from './components/loading/loading.component';
import { CandidateStatsComponent } from './components/candidate-stats/candidate-stats.component';
import { EntretienDialogComponent } from './components/entretien-dialog/entretien-dialog.component';

// Material Module
import { MaterialModule } from './material.module';

// Services
import { AuthService } from './services/auth.service';
import { CvService } from './services/cv.service';
import { EntretienService } from './services/entretien.service';
import { ApiService } from './services/api.service';
import { AlertsService } from './services/alerts.service';
import { OffreService } from './services/offre.service';
import { PostulationService } from './services/postulation.service';
import { StatsService } from './services/stats.service';
import { CandidatService } from './services/candidat.service';
import { RecruteurService } from './services/recruteur.service';

// Interceptors
import { ErrorInterceptor } from './interceptors/error.interceptor';
import { AuthInterceptor } from './interceptors/auth.interceptor';

// Guards
import { AuthGuard } from './guards/auth.guard';

// Feature Modules
import { CandidateModule } from './pages/candidate/candidate.module';
import { RecruteurModule } from './pages/recruteur/recruteur.module';
import { ResetPasswordComponent } from './pages/authentification/reset-password/reset-password.component';

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    HomeComponent,
    LoginComponent,
    RegisterComponent,
    ForgotPasswordComponent,
    JobCardComponent,
    ContactComponent,
    AProposComponent,
    PostulationDialogComponent,
    ConfirmDialogComponent,
    LoadingComponent,
    CandidateStatsComponent,
    EntretienDialogComponent,
    ResetPasswordComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    BrowserAnimationsModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    CandidateModule,
    RecruteurModule,
    JwtModule.forRoot({
      config: {
        tokenGetter: () => localStorage.getItem('token'),
        allowedDomains: ['localhost:8081'],
        disallowedRoutes: [
          '/api/auth/login',
          '/api/auth/register',
          '/api/auth/refresh-token',
          '/api/auth/forgot-password',
          '/api/auth/reset-password'
        ]
      }
    })
  ],
  providers: [
    AuthService,
    CvService,
    EntretienService,
    ApiService,
    AuthGuard,
    AlertsService,
    OffreService,
    PostulationService,
    StatsService,
    CandidatService,
    RecruteurService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }

// ============================================================================
// VARIABLES SCSS - Configuration globale du projet
// ============================================================================

// Couleurs principales
$primary-color: #2196f3;
$secondary-color: #1976d2;
$accent-color: #e95793;
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;
$info-color: #2196f3;

// Couleurs de texte
$text-color: #333;
$text-light: #666;
$text-muted: #999;
$text-white: #fff;

// Couleurs de fond
$background-primary: #fff;
$background-light: #f5f5f5;
$background-dark: #333;
$background-overlay: rgba(0, 0, 0, 0.5);

// Couleurs de bordure
$border-color: #e0e0e0;
$border-light: #f0f0f0;
$border-dark: #ccc;

// Gradients
$gradient-primary: linear-gradient(135deg, #e95793, #e95793);
$gradient-secondary: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
$gradient-accent: linear-gradient(135deg, #ec5f97, #a347d8);

// Typographie
$font-family-primary: 'Roboto', 'Helvetica Neue', sans-serif;
$font-family-secondary: 'Poppins', sans-serif;

$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.75rem;   // 28px
$font-size-4xl: 2rem;      // 32px
$font-size-5xl: 2.5rem;    // 40px
$font-size-6xl: 3rem;      // 48px

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.6;

// Espacements
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-full: 50%;

// Ombres
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
$shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.2);
$shadow-2xl: 0 16px 32px rgba(0, 0, 0, 0.25);

// Z-index
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// Breakpoints
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1400px;

// Transitions
$transition-fast: 0.15s ease-in-out;
$transition-normal: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// Conteneurs
$container-max-width: 1200px;
$container-padding: $spacing-md;

// Grille
$grid-columns: 12;
$grid-gap: $spacing-lg;

// Formulaires
$input-height: 48px;
$input-padding: $spacing-md;
$input-border-width: 1px;
$input-focus-border-color: $primary-color;

// Boutons
$button-height: 48px;
$button-padding-x: $spacing-lg;
$button-padding-y: $spacing-md;
$button-border-radius: $border-radius-md;

// Cartes
$card-padding: $spacing-lg;
$card-border-radius: $border-radius-md;
$card-shadow: $shadow-md;

// Header/Footer
$header-height: 80px;
$footer-height: 60px;

// Variables CSS personnalisées pour la compatibilité
:root {
  --primary-color: #{$primary-color};
  --secondary-color: #{$secondary-color};
  --accent-color: #{$accent-color};
  --success-color: #{$success-color};
  --warning-color: #{$warning-color};
  --error-color: #{$error-color};
  --text-color: #{$text-color};
  --text-light: #{$text-light};
  --background-light: #{$background-light};
  --border-color: #{$border-color};
  --shadow-md: #{$shadow-md};
  --border-radius-md: #{$border-radius-md};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --transition-normal: #{$transition-normal};
}

import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EntretienService } from '../../services/entretien.service';
import { AlertsService } from '../../services/alerts.service';

@Component({
  selector: 'app-entretien-dialog',
  template: `
    <h2 mat-dialog-title>Entretien IA</h2>
    <mat-dialog-content>
      <div *ngIf="!isStarted" class="text-center">
        <p>Vous êtes sur le point de commencer votre entretien IA.</p>
        <p>L'entretien durera environ 15 minutes.</p>
        <p>Assurez-vous d'être dans un endroit calme avec un bon microphone.</p>
        <button mat-raised-button color="primary" (click)="startInterview()">
          Commencer l'entretien
        </button>
      </div>

      <div *ngIf="isStarted && !isFinished" class="interview-container">
        <div class="question-container">
          <h3>Question {{currentQuestionIndex + 1}}/{{totalQuestions}}</h3>
          <p class="question-text">{{currentQuestion}}</p>
          
          <div class="recording-status" *ngIf="isRecording">
            <mat-progress-bar mode="indeterminate"></mat-progress-bar>
            <p>Enregistrement en cours...</p>
          </div>

          <div class="controls">
            <button mat-button color="primary" (click)="startRecording()" [disabled]="isRecording">
              <mat-icon>mic</mat-icon> Démarrer
            </button>
            <button mat-button color="warn" (click)="stopRecording()" [disabled]="!isRecording">
              <mat-icon>stop</mat-icon> Arrêter
            </button>
          </div>
        </div>
      </div>

      <div *ngIf="isFinished" class="text-center">
        <mat-icon class="success-icon">check_circle</mat-icon>
        <h3>Entretien terminé !</h3>
        <p>Merci d'avoir participé à cet entretien.</p>
        <p>Les résultats seront analysés et vous recevrez un retour sous peu.</p>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="closeDialog()">Fermer</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .interview-container {
      padding: 20px;
    }
    .question-container {
      margin: 20px 0;
    }
    .question-text {
      font-size: 1.2em;
      margin: 20px 0;
    }
    .recording-status {
      margin: 20px 0;
    }
    .controls {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin: 20px 0;
    }
    .success-icon {
      font-size: 48px;
      color: #4CAF50;
      margin: 20px 0;
    }
    .text-center {
      text-align: center;
      padding: 20px;
    }
  `]
})
export class EntretienDialogComponent implements OnInit {
  isStarted = false;
  isFinished = false;
  isRecording = false;
  currentQuestionIndex = 0;
  totalQuestions = 5;
  currentQuestion = '';
  questions: string[] = [
    'Pouvez-vous vous présenter brièvement et nous parler de votre parcours professionnel ?',
    'Quelles sont vos principales compétences techniques ?',
    'Comment gérez-vous les situations de stress au travail ?',
    'Pourquoi souhaitez-vous rejoindre notre entreprise ?',
    'Où vous voyez-vous dans 5 ans ?'
  ];

  constructor(
    public dialogRef: MatDialogRef<EntretienDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { offreId: number },
    private entretienService: EntretienService,
    private alertsService: AlertsService
  ) {}

  ngOnInit() {
    this.currentQuestion = this.questions[0];
  }

  startInterview() {
    this.isStarted = true;
    // Initialiser l'API de reconnaissance vocale si nécessaire
  }

  startRecording() {
    this.isRecording = true;
    // Logique pour démarrer l'enregistrement
  }

  stopRecording() {
    this.isRecording = false;
    // Logique pour arrêter l'enregistrement et envoyer à l'API
    this.processAnswer();
  }

  private processAnswer() {
    // Simuler le traitement de la réponse
    setTimeout(() => {
      if (this.currentQuestionIndex < this.totalQuestions - 1) {
        this.currentQuestionIndex++;
        this.currentQuestion = this.questions[this.currentQuestionIndex];
      } else {
        this.isFinished = true;
        this.saveInterview();
      }
    }, 1000);
  }

  private saveInterview() {
    this.entretienService.saveInterview(this.data.offreId).subscribe({
      next: (response) => {
        this.alertsService.showSuccess('Entretien enregistré avec succès');
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors de l\'enregistrement de l\'entretien');
      }
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }
} 
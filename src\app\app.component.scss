.center-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }

  // Layout pour l'application
  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  // Background global pour la page d'accueil - utilise l'image définie dans styles.scss
  .app-container.home-page {
    // L'image de fond est déjà définie globalement dans styles.scss
    // On s'assure juste que le conteneur prend toute la hauteur
    min-height: 100vh;
  }

  router-outlet + * {
    flex: 1;
  }
  

<!-- Header du dashboard candidat -->
<header class="candidate-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand" routerLink="/">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Navigation et profil à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover">
          Accueil
        </a>

        <!-- Menu profil -->
        <div class="profile-menu-container">
          <button mat-icon-button
                 [matMenuTriggerFor]="profileMenu"
                 aria-label="Profil utilisateur"
                 class="profile-button">
            <div class="profile-avatar">
              <mat-icon>person</mat-icon>
            </div>
          </button>

          <mat-menu #profileMenu="matMenu" xPosition="before" class="profile-dropdown">
            <button mat-menu-item routerLink="/candidate/profile">
              <mat-icon>account_circle</mat-icon>
              <span>Mon profil</span>
            </button>
            <button mat-menu-item routerLink="/candidate/cv">
              <mat-icon>description</mat-icon>
              <span>Mon CV</span>
            </button>
            <button mat-menu-item routerLink="/candidate/postulations">
              <mat-icon>work</mat-icon>
              <span>Mes postulations</span>
            </button>
            <button mat-menu-item routerLink="/candidate/saved-offers">
              <mat-icon>bookmark</mat-icon>
              <span>Offres sauvegardées</span>
            </button>
            <button mat-menu-item routerLink="/candidate/change-password">
              <mat-icon>lock</mat-icon>
              <span>Changer mot de passe</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item (click)="logout()" routerLink="/login">
              <mat-icon>exit_to_app</mat-icon>
              <span>Se déconnecter</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>

<!-- Contenu principal du dashboard -->
<main class="dashboard-main">
  <div class="container">
    <!-- Section de bienvenue -->
    <div class="welcome-section">
      <h1>Bienvenue dans votre espace candidat</h1><br>
      <p>Trouvez l'emploi qui correspond à vos compétences et aspirations</p>
    </div>

    <!-- Barre de recherche -->
    <div class="search-section">
      <mat-form-field appearance="outline" class="search-field">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput
               placeholder="Rechercher par mot-clé, compétence, entreprise, localisation..."
               [(ngModel)]="searchText"
               (keyup)="applyFilter()">
        <mat-hint>Essayez des termes comme "Développeur", "Rabat", ou "CDI"</mat-hint>
      </mat-form-field>
    </div>

    <!-- Liste des offres d'emploi -->
    <div class="offers-section">
      <h2>Offres d'emploi disponibles</h2>

      <div class="job-list">
        <div class="job-card" *ngFor="let offre of filteredOffres">
          <div class="job-main-content">
            <div class="job-info">
              <h3 class="job-title">{{ offre.titre }}</h3>
              <div class="job-details">
                <p class="company">Recruteur ID: {{ offre.recruteurId }}</p>
                <div class="location-contract">
                  <p>{{ offre.ville }} <span class="dot">•</span> {{ offre.typeContrat }}</p>
                </div>
              </div>
            </div>
            <button class="view-button" (click)="offre.expanded = !offre.expanded">
              {{ offre.expanded ? 'Voir moins' : 'Voir plus' }}
            </button>
          </div>

          <!-- Contenu détaillé -->
          <div class="job-expanded-content" *ngIf="offre.expanded">
            <div class="divider"></div>

            <div class="additional-details">
              <div class="detail-item" *ngIf="offre.salaire">
                <h4>Salaire</h4>
                <p>{{ offre.salaire }} MAD/an</p>
              </div>

              <div class="detail-item">
                <h4>Type de contrat</h4>
                <p>{{ offre.typeContrat }}</p>
              </div>

              <div class="detail-item">
                <h4>Domaine</h4>
                <p>{{ offre.domaine }}</p>
              </div>

              <div class="detail-item" *ngIf="offre.heuresParSemaine">
                <h4>Heures par semaine</h4>
                <p>{{ offre.heuresParSemaine }} heures</p>
              </div>

              <div class="detail-item">
                <h4>Date limite</h4>
                <p>{{ offre.dateLimite | date:'dd/MM/yyyy' }}</p>
              </div>
            </div>

            <div class="job-description">
              <h4>Description du poste</h4>
              <p>{{ offre.description }}</p>
            </div>

            <div class="job-requirements">
              <h4>Compétences requises</h4>
              <div class="competences-list">
                <span class="competence-chip" *ngFor="let competence of offre.competences">
                  {{ competence }}
                </span>
              </div>
            </div>

            <div class="action-buttons">
              <button class="apply-button" (click)="postuler(offre)">
                <mat-icon>send</mat-icon>
                Postuler
              </button>
              <button class="save-button" (click)="sauvegarderOffre(offre)">
                <mat-icon>bookmark</mat-icon>
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- Composants de chargement -->
<app-loading [isLoading]="isLoading" [message]="'Chargement des offres...'"></app-loading>
<app-loading [isLoading]="isFiltering" [message]="'Application des filtres...'"></app-loading>
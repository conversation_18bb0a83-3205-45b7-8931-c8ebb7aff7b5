// Interview Component - Compatible avec background global
:host {
  display: block;
  min-height: calc(100vh - 80px);

  header {
    background-color: transparent;
    border: none;
    box-shadow: none;
  }

  .navbar {
    padding: 0.5rem 1rem;
    border: none;
  }

  .logo-container {
    display: flex;
    align-items: center;
  }

  .logo-image {
    max-height: 50px;
    width: auto;
  }

  .nav-item {
    color: white;
    font-weight: 500;
    transition: color 0.3s ease;

    &:hover {
      color: var(--accent-color);
    }
  }
}

// -------------------------------------------------------------------------

// /* Variables pour les couleurs */
// $primary-color: #3f51b5;
// $accent-color: #ff4081;
// $text-light: #ffffff;
// $text-dark: #333333;
// $modal-bg: #ffffff;
// $overlay-bg: rgba(0, 0, 0, 0.6);

// .interview-container {
//   width: 100%;
//   min-height: 100vh;
//   // background-color: #f5f5f5;
//   position: relative;
// }

// /* Step 1 - Welcome Screen */
// .welcome-screen {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   min-height: 100vh;
//   // background: linear-gradient(135deg, $primary-color, darken($primary-color, 20%));
// }

// .welcome-card {
//   background-color: white;
//   padding: 2rem;
//   border-radius: 8px;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//   text-align: center;
//   max-width: 500px;
//   width: 90%;
// }

// .welcome-title {
//   color: $text-dark;
//   margin-bottom: 1.5rem;
//   font-size: 1.8rem;
// }

// .start-button {
//   padding: 0.75rem 2rem;
//   font-size: 1.1rem;
//   transition: all 0.3s ease;
  
//   &:hover {
//     transform: translateY(-2px);
//     box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
//   }
// }

// /* Modal */
// .modal-container {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   z-index: 1000;
// }

// .overlay {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: $overlay-bg;
//   animation: fadeIn 0.3s ease;
// }

// .modal {
//   background-color: $modal-bg;
//   padding: 2rem;
//   border-radius: 10px;
//   box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
//   width: 90%;
//   max-width: 550px;
//   position: relative;
//   z-index: 1001;
//   animation: slideIn 0.4s ease;
// }

// .modal-title {
//   color: $primary-color;
//   margin-bottom: 1.5rem;
//   font-size: 1.5rem;
//   border-bottom: 2px solid rgba($primary-color, 0.2);
//   padding-bottom: 0.75rem;
// }

// .modal-content {
//   p {
//     margin-bottom: 1.5rem;
//     line-height: 1.6;
//   }
// }

// .confirm-section {
//   margin: 1.5rem 0;
// }

// .button-section {
//   margin-top: 1.5rem;
//   display: flex;
//   justify-content: center;
// }

// /* Step 2 - Interview Screen */
// .interview-screen {
//   padding: 2rem;
//   max-width: 1200px;
//   margin: 0 auto;
// }

// .interview-title {
//   color: $primary-color;
//   margin-bottom: 2rem;
//   font-size: 2rem;
//   text-align: center;
// }

// .interview-content {
//   background-color: white;
//   padding: 2rem;
//   border-radius: 8px;
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
// }

// /* Animations */
// @keyframes fadeIn {
//   from {
//     opacity: 0;
//   }
//   to {
//     opacity: 1;
//   }
// }

// @keyframes slideIn {
//   from {
//     transform: translateY(-20px);
//     opacity: 0;
//   }
//   to {
//     transform: translateY(0);
//     opacity: 1;
//   }
// }

// /* Responsive adjustments */
// @media (max-width: 768px) {
//   .modal {
//     padding: 1.5rem;
//   }
  
//   .welcome-card {
//     padding: 1.5rem;
//   }
  
//   .welcome-title, .interview-title {
//     font-size: 1.5rem;
//   }
  
//   .start-button {
//     padding: 0.5rem 1.5rem;
//   }
// }

.entretien-container {
  max-width: 800px;
  min-height: 400px;
  position: relative;
  margin: 2rem auto;
  padding: 2rem;
  // background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .etape {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    height: 100%;

    h1, h2 {
      color: white;
      margin-bottom: 1rem;
    }
  }

  .etape-accueil {
    justify-content: center;
    padding-top: 4.5rem;
    align-items: center;
    text-align: center;
  }

  .conditions-list {
    // background-color: white;
    padding: 1.5rem;
    border-radius: 6px;
    border-left: 4px solid green;
    justify-content: flex-start;

    ul {
      padding-left: 1.5rem;
      margin: 0;

      li{
        margin-bottom: 0.5rem;
        line-height: 1.5;
        color: white ;
      }
    }
  }

  .confirmation-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
    }

    label {
      font-size: 1rem;
      color: aliceblue;
    }
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;

    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    &-primary {
      background-color: orangered;
      color: white;

    &:hover {
      background-color: orangered;
      color: #ffffff;
      box-shadow: 0 6px 14px orangered; 
      transform: scale(1.05);
    }
  
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(0, 120, 215, 0.4);
    }

      &:disabled {
        background-color: gainsboro;
        cursor: not-allowed;
        box-shadow: 0 1px 3px rgb(160, 124, 58);
      }
    }
  }
  /* Nouveaux styles pour la checkbox verte */
.green-checkbox {
  accent-color: green; /* Couleur verte pour la checkbox */
  transform: scale(1.2); /* Légère augmentation de taille */
  margin-right: 8px;
}

.green-label {
  color: green; /* Couleur verte pour le texte */
  font-weight: 500; /* Légère augmentation du poids de police */
}
p{
  color: white;
}
}

// ---------------------------------------------------------------------

/* Variables de thème */
.entretien-container {
  background: linear-gradient(120deg, #6a0572 0%, #ab47bc 100%);
  border-radius: 24px;
  padding: 32px 24px;
  color: #fff;
  max-width: 650px;
  margin: 50px auto;
  box-shadow: 0 2px 20px #48036d33;
}

.ai-block, .candidat-block {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
}

.ai-block .ai-mic-icon,
.candidat-block .mic-icon {
  background: #fff;
  border-radius: 50%;
  padding: 12px;
  margin-right: 18px;
  position: relative;
  box-shadow: 0 2px 12px #ab47bc50;
}

.ai-block.active .ai-mic-icon,
.candidat-block.active .mic-icon {
  animation: pulse-mic 1s infinite;
}

@keyframes pulse-mic {
  0%, 100% { box-shadow: 0 0 0 0 #ab47bc70; }
  70% { box-shadow: 0 0 0 14px #ab47bc20; }
}

.wave, .ai-wave {
  position: absolute;
  bottom: -12px; left: 50%;
  transform: translateX(-50%);
  height: 14px; width: 48px; border-radius: 8px;
  background: linear-gradient(90deg, #ab47bc 20%, #ffecb3 100%);
  animation: wave-move 1.2s infinite linear;
  opacity: 0.7;
}
@keyframes wave-move {
  0% { width: 48px; opacity: .7;}
  50% { width: 60px; opacity: 1;}
  100% { width: 48px; opacity: .7;}
}

.voice-control-btn {
  background: #ab47bc;
  color: #fff;
  border: none;
  border-radius: 40px;
  padding: 13px 22px;
  margin-left: 8px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 12px #ab47bc50;
  transition: background 0.2s;
}
.voice-control-btn:disabled {
  background: #aaa;
  color: #fff;
  cursor: not-allowed;
}
.voice-control-btn:hover:not(:disabled) {
  background: #6a0572;
}

.pulse-circle {
  display: inline-block;
  width: 14px; height: 14px; border-radius: 50%;
  background: #ffecb3;
  margin-left: 7px;
  animation: pulse 1s infinite;
}
@keyframes pulse {
  0%, 100% { box-shadow: 0 0 0 0 #ffecb350; }
  70% { box-shadow: 0 0 0 8px #ffecb320; }
}

.transcript-container {
  background: rgba(255,255,255,0.06);
  border-radius: 10px;
  margin-top: 28px;
  padding: 16px 18px 10px 18px;
  min-height: 75px;
  width: 100%;
  height: auto;
}

.transcript-content.empty {
  color: #eedff5;
  opacity: .6;
}

@media (max-width: 650px) {
  .entretien-container { padding: 10px 2vw; }
}

// !------------------------------------------------------

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// État de la connexion
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #4CAF50;
  color: white;
  font-size: 0.9rem;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.offline {
    background-color: #f44336;
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

// Message d'erreur
.error-message {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #f44336;
  color: white;
  border-radius: 4px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

// Indicateur de progression
.progress-indicator {
  margin-bottom: 24px;
  
  .question-counter {
    display: block;
    text-align: center;
    color: #ab47bc;
    font-size: 1.1rem;
    margin-bottom: 8px;
  }

  mat-progress-bar {
    height: 8px;
    border-radius: 4px;
  }
}

// Statut de l'IA
.ai-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #ab47bc;
  font-size: 0.9rem;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

// Statut d'enregistrement
.recording-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #6a0572;
  font-size: 0.9rem;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    animation: pulse 1s infinite;
  }
}

// Boutons de contrôle
.control-buttons {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  gap: 16px;

  .submit-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 1.1rem;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}

// Overlay de chargement
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: white;
  z-index: 1000;
  border-radius: 8px;

  span {
    font-size: 1.1rem;
  }
}

// États d'erreur
.ai-block.error,
.candidat-block.error {
  opacity: 0.7;
  pointer-events: none;
}

// Améliorations des animations
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .connection-status {
    top: auto;
    bottom: 20px;
    right: 20px;
  }

  .error-message {
    width: 90%;
    text-align: center;
  }

  .control-buttons {
    flex-direction: column;
    
    .submit-btn {
      width: 100%;
    }
  }
}
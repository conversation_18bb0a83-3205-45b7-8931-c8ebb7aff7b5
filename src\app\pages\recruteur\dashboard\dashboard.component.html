<!-- header.component.html -->
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover">
          Accueil
        </a>

        <!-- Bouton profil -->
        <div class="profile-menu-container">
          <button mat-icon-button 
                 [matMenuTriggerFor]="profileMenu" 
                 aria-label="Profil utilisateur"
                 class="profile-button">
            <div class="profile-avatar">
              <mat-icon>person</mat-icon>
            </div>
          </button>
          
          <mat-menu #profileMenu="matMenu" xPosition="before" class="profile-dropdown">
            <button mat-menu-item routerLink="/recruteur/interview-results">
              <mat-icon>account_circle</mat-icon>
              <span>Tes infos</span>
            </button>
            <!-- <button mat-menu-item routerLink="/cv">
              <mat-icon>description</mat-icon>
              <span>Ton CV</span>
            </button> -->
            <button mat-menu-item routerLink="/recruteur/change-password-recruteur">
              <mat-icon>lock</mat-icon>
              <span>Changer mot de passe</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item routerLink="/login" (click)="logout()">
              <mat-icon>exit_to_app</mat-icon>
              <span>Se déconnecter</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>

<!-- ---------------------------------------------------------- -->



<!-- ---------------------------------------------------------- -->



<!-- <p>dashboard de recruteur works!</p> -->
<div class="recruteur-container">
    <div class="header">
      <h1 style="text-align: left;margin-bottom: 1.5rem;">Bievenue Mr. <b>Recruteur</b><br> Dans Votre Interface !</h1><br>
      <!-- <img src="assets/images/loogo.png" alt="Logo RecrutIQ" class="logo" appImageSizing/> -->
      <div class="header-actions">
        <h1>Mes Offres d’Emploi</h1>
        <div class="action-buttons">
          <button mat-icon-button
                  (click)="refreshOffres()"
                  matTooltip="Rafraîchir les offres"
                  [disabled]="isLoading">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>
      <button mat-raised-button color="primary" class="ajouter-btn" routerLink="/recruteur/post-job">
        + Ajouter une offre
      </button>
    </div>
  
    <!-- Indicateur de chargement -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner></mat-spinner>
      <p>Chargement des offres...</p>
    </div>

    <!-- Message si aucune offre -->
    <div *ngIf="!isLoading && offres.length === 0" class="no-offers">
      <mat-icon>work_off</mat-icon>
      <h3>Aucune offre d'emploi</h3>
      <p>Vous n'avez pas encore créé d'offres d'emploi.</p>
      <button mat-raised-button color="primary" routerLink="/recruteur/post-job">
        Créer votre première offre
      </button>
    </div>

    <!-- Grille des offres -->
    <div class="offres-grid" *ngIf="!isLoading && offres.length > 0">
      <mat-card class="offre-card"
                [class.nouvelle-offre]="isNouvelleOffre(offre.id)"
                *ngFor="let offre of offres">
        <mat-card-header>
          <mat-card-title>{{ offre.titre }}</mat-card-title>
          <mat-card-subtitle>
            <mat-icon>location_on</mat-icon> {{ offre.ville }}
            <br>
            <mat-icon>domain</mat-icon> {{ offre.domaine }}
            <br>
            <mat-icon>schedule</mat-icon> {{ offre.typeContrat }}
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="offre-details">
            <p><strong>Salaire:</strong> {{ offre.salaire || 'Non spécifié' }}</p>
            <p><strong>Compétences:</strong> {{ getCompetencesText(offre.competences) }}</p>
            <p><strong>Date limite:</strong> {{ offre.dateLimite | date:'dd/MM/yyyy' }}</p>
          </div>
          <!-- Statut de l'offre (utilisation temporaire de expanded comme statut) -->
          <div class="etat" [ngClass]="offre.expanded ? 'Inactif' : 'Actif'">
            {{ offre.expanded ? 'Inactif' : 'Actif' }}
          </div>
        </mat-card-content>
        <mat-card-actions>
          <!-- Bouton de modification - redirige vers post-job avec données pré-remplies -->
          <button mat-icon-button
                  (click)="editOffre(offre)"
                  matTooltip="Modifier l'offre"
                  color="primary"
                  [disabled]="isLoading">
            <mat-icon>edit</mat-icon>
          </button>

          <!-- Bouton pour changer le statut -->
          <button mat-icon-button
                  (click)="toggleOffreStatus(offre)"
                  [matTooltip]="offre.expanded ? 'Activer l\'offre' : 'Désactiver l\'offre'"
                  [color]="offre.expanded ? 'primary' : 'accent'"
                  [disabled]="isLoading">
            <mat-icon>{{ offre.expanded ? 'play_arrow' : 'pause' }}</mat-icon>
          </button>

          <!-- Bouton de suppression - action de suppression avec confirmation -->
          <button mat-icon-button
                  (click)="deleteOffre(offre.id)"
                  matTooltip="Supprimer l'offre"
                  color="warn"
                  [disabled]="isLoading">
            <mat-icon>delete</mat-icon>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
  

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container-t {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}
.about-us-container {       
  
  .underline-small {
    width: 50px;
    height: 3px;
  }

  // Section avec image et contenu
  .about-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px;

    .about-image {
      width: 100%;
      height: auto;
      max-height: 400px;
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
       .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      color: white;
      padding: 2rem;
      transform: translateY(100%);
      transition: transform 0.3s ease;

      .overlay-content {
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          opacity: 0.9;
        }
      }
    }

    &:hover .image-overlay {
      transform: translateY(0);
    }
  }

  .about-content {
    padding-left: 2rem;

    .about-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1.5rem;
      background: #ff5f45;;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .about-text {
      font-size: 1.1rem;
      line-height: 1.8;
      color: white;
      margin-bottom: 1.5rem;
      text-align: justify;
    }
  }

  // Responsive pour la section image
  @media (max-width: 768px) {
    .about-content {
      padding-left: 0;
      margin-top: 2rem;
      text-align: center;

      .about-title {
        font-size: 2rem;
      }

      .about-text {
        text-align: left;
      }
    }
  }

  .social-card {
    padding: 25px;
    transition: transform 0.3s, box-shadow 0.3s, background-color 0.3s;
    border: none;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
      background-color: rgba(255, 255, 255, 0.15);
    }

    &.clickable-card {
      cursor: pointer;

      &:hover {
        transform: translateY(-15px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        background-color: rgba(255, 255, 255, 0.2);

        .social-link-indicator {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }

    .social-icon {
      margin-bottom: 20px;

      i {
        display: inline-block;
      }
    }

    .platform-title {
      margin-top: 15px;
      margin-bottom: 15px;
      font-weight: 600;
      color: white;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    .platform-description {
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    .social-link-indicator {
      margin-top: 15px;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
      color: #ff5f45;
      font-size: 0.9rem;
      font-weight: 500;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);

      i {
        margin-right: 5px;
        font-size: 0.8rem;
      }
    }
  }
}  

/* Custom colors for social platforms */
.fa-facebook-f {   
  color: #3b5998; 
}  

.fa-twitter {   
  color: #1da1f2; 
} 

.fa-youtube {   
  color: #ff0000; 
}
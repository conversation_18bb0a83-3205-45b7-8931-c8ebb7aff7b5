<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>

<div class="login-container">
    <!-- <span class="logo">
      <img src="assets/images/loogo.png" alt="RecrutIQ Logo">
    </span> -->

    <h2>Connexion</h2>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="off">

      <!-- -------------------------------------------------------------------- -->

      <div class="social-login mb-3">
        <button mat-raised-button class="w-100 mb-2 google-btn" (click)="loginWithGoogle()">
          <i class="fab fa-google me-2"></i>
          Continuer avec Google
        </button>
        
        <!-- <button mat-raised-button class="w-100 mb-2 gmail-btn" (click)="loginWithGmail()">
          <mat-icon svgIcon="gmail"></mat-icon>
          Continuer avec Gmail
        </button> -->

        <button mat-raised-button class="w-100 mb-3 microsoft-btn" (click)="loginWithMicrosoft()">
          <i class="fab fa-microsoft me-2"></i>
          Continuer avec Microsoft
        </button>
        
        <button mat-raised-button class="w-100 mb-2 facebook-btn" (click)="loginWithFacebook()">
          <i class="fab fa-facebook-f me-2"></i>
          Continuer avec Facebook
        </button>
        
 
      </div>
      
      <!-- Divider -->
      <div class="divider d-flex align-items-center mb-3">
        <span class="divider-text">ou</span>
      </div>

      <!-- --------------------------------------------------------------------------- -->

      <div class="form-group input-icon-wrapper">
        <input
          type="email"
          formControlName="email"
          placeholder="Adresse e-mail"
          [ngClass]="{'invalid': loginForm.get('email')?.invalid && loginForm.get('email')?.touched}"

          (focus)="setFieldFocus('email', true)"
          (blur)="setFieldFocus('email', false)"

          autocomplete="off"
          >
        <i class="bi bi-person icon-right"></i>
      </div>

      <div class="form-group password-field input-icon-wrapper">
        <input
          [type]="showPassword ? 'text' : 'password'"
          formControlName="password"
          placeholder="Mot de passe"
          [ngClass]="{'invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"

          (focus)="setFieldFocus('password', true)"
          (blur)="setFieldFocus('password', false)"
          (input)="onPasswordInput($event)"

          autocomplete="new-password"
          >

        <!-- Icône lock fixe quand le champ est vide -->
        <i class="bi bi-lock icon-right" *ngIf="!hasPasswordValue"></i>

        <!-- Bouton show/hide qui apparaît quand on tape -->
        <button
          type="button"
          class="show-password"
          *ngIf="hasPasswordValue"
          (click)="togglePasswordVisibility()">
          <i class="bi bi-eye" *ngIf="!showPassword"></i>
          <i class="bi bi-eye-slash" *ngIf="showPassword"></i>
        </button>

      </div>
      
      <button type="submit" class="login-button">Se Connecter</button>
    </form>
    
    <div class="forgot-password">
      <a (click)="navigateToForgotPassword()">Mot de passe oublié ?</a>
    </div>
    
<!-- Divider -->
<div class="divider d-flex align-items-center mb-3">
  <span class="divider-text">Nouveau à RecrutIQ</span>
</div>

    <div class="register-link">
      <span>Vous n'avez pas de compte ? </span>
      <a (click)="navigateToRegister()">S'inscrire</a>
    </div>

  </div>
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container-t">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>
      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
      </div>
    </nav>
  </div>
</header>
<div class="about-us-container">
  <!-- Section avec image -->
  <div class="container py-5">
    <div class="row align-items-center mb-5">
      <div class="col-md-6">
        <div class="about-image-container">
          <img src="assets/images/about_img.png"
               alt="À propos de RecrutIQ - Plateforme IA de recrutement"
               class="about-image img-fluid rounded shadow">
                         <div class="image-overlay">
            <div class="overlay-content">
              <h3>RecrutIQ : L'avenir du recrutement</h3>
              <p>Plateforme IA révolutionnaire qui transforme la façon dont les entreprises trouvent leurs talents exceptionnels</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="about-content">
          <h2 class="about-title">Notre Mission</h2>
          <p class="about-text">
            RecrutIQ révolutionne le processus de recrutement en utilisant l'intelligence artificielle
            pour connecter les meilleurs talents avec les opportunités parfaites. Notre plateforme
            innovante simplifie et optimise chaque étape du recrutement.
          </p>
          <p class="about-text">
            Nous croyons en un avenir où la technologie et l'humain travaillent ensemble pour
            créer des correspondances parfaites entre candidats et employeurs.
          </p>
        </div>
      </div>
    </div>

    <!-- Section des plateformes sociales -->
    <div class="row">
      <div class="col-md-4" *ngFor="let platform of socialPlatforms">
        <mat-card class="social-card h-100 clickable-card" (click)="openSocialLink(platform)">
          <mat-card-content class="text-center">
            <div class="social-icon mb-3">
              <i [class]="platform.icon" class="fa-2x"></i>
            </div>
            <h2 class="platform-title">{{ platform.name }}</h2>
            <p class="platform-description">{{ platform.description }}</p>
            <div class="social-link-indicator">
              <i class="fas fa-external-link-alt"></i>
              <span>Cliquez pour visiter</span>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>
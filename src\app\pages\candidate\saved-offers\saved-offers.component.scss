// Saved Offers Component - Compatible avec background global
.saved-offers-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  min-height: calc(100vh - 80px);
  background-color: transparent;
}

.saved-offers-header {
  text-align: center;
  margin-bottom: 3rem;
  background-color: transparent;
  // backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  h1 {
    color: violet;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 600;
  }

  p {
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
  }
}

.offers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.offer-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.1) !important;
  // backdrop-filter: blur(10px);
  border-radius: 12px;
  // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  mat-card-header {
    margin-bottom: 1rem;
    padding: 1.5rem 1.5rem 0;

    mat-card-title {
      color: var(--primary-color);
      font-size: 1.4rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    mat-card-subtitle {
      color: var(--text-light);
      font-size: 1rem;
    }
  }

  mat-card-content {
    flex: 1;
    padding: 0 1.5rem;
  }
}

.offer-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #ecf0f1;

  > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
      color: #7f8c8d;
    }
  }

  .offer-deadline {
    &.expired {
      color: #e74c3c;
    }
  }
}

.offer-description {
  margin: 1rem 0;
  color: white;
  font-size: 0.95rem;
  line-height: 1.5;
}

.offer-skills {
  margin: 1rem 0;

  mat-chip-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  mat-chip {
    font-size: 0.8rem;
  }
}

.offer-benefits {
  margin: 1rem 0;
  padding-top: 1rem;
  border-top: 1px solid #ecf0f1;

  h4 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: white;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
        color: green;
      }
    }
  }
}

mat-card-actions {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  margin-top: auto;

  button {
    &.mat-raised-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    &.mat-icon-button {
      flex: 0 0 auto;
    }
  }
}

.no-saved-offers {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 3rem 1rem;
  color: white;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 1rem;
    color: #bdc3c7;
  }

  p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    color: #ecf0f1;
  }

  button {
    min-width: 200px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .saved-offers-container {
    margin: 1rem auto;
  }

  .offers-grid {
    grid-template-columns: 1fr;
  }

  .offer-card {
    mat-card-actions {
      flex-direction: column;

      button {
        width: 100%;
        margin: 0.25rem 0;
      }
    }
  }
} 
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface Offre {
  id: number;
  titre: string;
  description: string;
  domaine: string;
  ville: string;
  typeContrat: string;
  salaire?: string;
  heuresParSemaine?: number;
  dateLimite: Date;
  competences: string[];
  recruteurId: number;
  expanded?: boolean;
}

export interface OffreFilter {
  search?: string;
  domaine?: string;
  ville?: string;
  typeContrat?: string;
  dateLimite?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class OffreService {
  private apiUrl = `${environment.apiUrl}/offres`;

  // BehaviorSubject pour partager les données d'édition entre composants
  private offreToEditSubject = new BehaviorSubject<any>(null);
  public offreToEdit$ = this.offreToEditSubject.asObservable();

  // BehaviorSubject pour notifier les changements dans les offres
  private offresUpdatedSubject = new BehaviorSubject<boolean>(false);
  public offresUpdated$ = this.offresUpdatedSubject.asObservable();

  // BehaviorSubject pour gérer la liste des offres en temps réel
  private offresListSubject = new BehaviorSubject<Offre[]>([]);
  public offresList$ = this.offresListSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Récupérer toutes les offres avec filtres optionnels
  getOffres(filter?: OffreFilter): Observable<Offre[]> {
    let params = new HttpParams();
    
    if (filter) {
      if (filter.search) {
        params = params.set('search', filter.search);
      }
      if (filter.domaine) {
        params = params.set('domaine', filter.domaine);
      }
      if (filter.ville) {
        params = params.set('ville', filter.ville);
      }
      if (filter.typeContrat) {
        params = params.set('typeContrat', filter.typeContrat);
      }
      if (filter.dateLimite) {
        params = params.set('dateLimite', filter.dateLimite.toISOString());
      }
    }

    return this.http.get<Offre[]>(this.apiUrl, { params });
  }

  // Récupérer une offre par son ID
  getOffreById(id: number): Observable<Offre> {
    return this.http.get<Offre>(`${this.apiUrl}/${id}`);
  }

  // Récupérer les offres sauvegardées par le candidat
  getOffresSauvegardees(): Observable<Offre[]> {
    return this.http.get<Offre[]>(`${this.apiUrl}/sauvegardees`);
  }

  // Sauvegarder une offre
  sauvegarderOffre(offreId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${offreId}/sauvegarder`, {});
  }

  // Supprimer une offre des sauvegardes
  supprimerSauvegarde(offreId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${offreId}/sauvegarder`);
  }

  // Récupérer les offres recommandées pour le candidat
  getOffresRecommandees(): Observable<Offre[]> {
    return this.http.get<Offre[]>(`${this.apiUrl}/recommandees`);
  }

  // Récupérer les offres similaires à une offre donnée
  getOffresSimilaires(offreId: number): Observable<Offre[]> {
    return this.http.get<Offre[]>(`${this.apiUrl}/${offreId}/similaires`);
  }

  // Récupérer les statistiques des offres (pour le recruteur)
  getStatistiquesOffres(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/statistiques`);
  }

  // ===== MÉTHODES SPÉCIFIQUES POUR LE RECRUTEUR =====

  // Récupérer les offres d'un recruteur spécifique
  getOffresByRecruteur(recruteurId: number): Observable<Offre[]> {
    return this.http.get<Offre[]>(`${this.apiUrl}/recruteur/${recruteurId}`);
  }

  // Créer une nouvelle offre
  createOffre(offre: any): Observable<Offre> {
    return this.http.post<Offre>(this.apiUrl, offre).pipe(
      tap((nouvelleOffre) => {
        // Notifier que les offres ont été mises à jour
        this.notifyOffresUpdated();
        // Ajouter la nouvelle offre à la liste locale
        this.addOffreToList(nouvelleOffre);
      })
    );
  }

  // Mettre à jour une offre existante
  updateOffre(offreId: number, offre: any): Observable<Offre> {
    return this.http.put<Offre>(`${this.apiUrl}/${offreId}`, offre).pipe(
      tap((offreModifiee) => {
        // Notifier que les offres ont été mises à jour
        this.notifyOffresUpdated();
        // Mettre à jour l'offre dans la liste locale
        this.updateOffreInList(offreModifiee);
      })
    );
  }

  // Supprimer une offre
  deleteOffre(offreId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${offreId}`).pipe(
      tap(() => {
        // Notifier que les offres ont été mises à jour
        this.notifyOffresUpdated();
        // Supprimer l'offre de la liste locale
        this.removeOffreFromList(offreId);
      })
    );
  }

  // Changer le statut d'une offre (Actif/Inactif)
  changeOffreStatus(offreId: number, status: string): Observable<Offre> {
    return this.http.patch<Offre>(`${this.apiUrl}/${offreId}/status`, { status });
  }

  // Méthodes pour la gestion de l'édition d'offres
  // Définir l'offre à éditer (appelée depuis le dashboard)
  setOffreToEdit(offre: any): void {
    this.offreToEditSubject.next(offre);
  }

  // Récupérer l'offre à éditer (appelée depuis post-job)
  getOffreToEdit(): any {
    return this.offreToEditSubject.value;
  }

  // Nettoyer les données d'édition après utilisation
  clearOffreToEdit(): void {
    this.offreToEditSubject.next(null);
  }

  // ===== MÉTHODES POUR LA GESTION DE LA LISTE DES OFFRES =====

  // Notifier que les offres ont été mises à jour
  notifyOffresUpdated(): void {
    this.offresUpdatedSubject.next(true);
  }

  // Mettre à jour la liste des offres
  updateOffresList(offres: Offre[]): void {
    this.offresListSubject.next(offres);
  }

  // Obtenir la liste actuelle des offres
  getCurrentOffresList(): Offre[] {
    return this.offresListSubject.value;
  }

  // Ajouter une nouvelle offre à la liste
  addOffreToList(nouvelleOffre: Offre): void {
    const currentOffres = this.getCurrentOffresList();
    const updatedOffres = [nouvelleOffre, ...currentOffres]; // Ajouter en premier
    this.updateOffresList(updatedOffres);
  }

  // Mettre à jour une offre dans la liste
  updateOffreInList(offreModifiee: Offre): void {
    const currentOffres = this.getCurrentOffresList();
    const index = currentOffres.findIndex(o => o.id === offreModifiee.id);
    if (index !== -1) {
      currentOffres[index] = offreModifiee;
      this.updateOffresList([...currentOffres]);
    }
  }

  // Supprimer une offre de la liste
  removeOffreFromList(offreId: number): void {
    const currentOffres = this.getCurrentOffresList();
    const filteredOffres = currentOffres.filter(o => o.id !== offreId);
    this.updateOffresList(filteredOffres);
  }

  // Créer une offre localement (pour les tests sans backend)
  createOffreLocally(offreData: any): Offre {
    const currentOffres = this.getCurrentOffresList();
    const newId = Math.max(...currentOffres.map(o => o.id), 0) + 1;

    const nouvelleOffre: Offre = {
      id: newId,
      titre: offreData.title,
      description: offreData.description,
      domaine: offreData.domaine,
      ville: offreData.city,
      typeContrat: offreData.contractType,
      salaire: offreData.salary,
      heuresParSemaine: 40,
      dateLimite: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
      competences: offreData.skills ? offreData.skills.split(',').map((s: string) => s.trim()) : [],
      recruteurId: 1, // ID temporaire
      expanded: false
    };

    this.addOffreToList(nouvelleOffre);
    this.notifyOffresUpdated();
    return nouvelleOffre;
  }
}
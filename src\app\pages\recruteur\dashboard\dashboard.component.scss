.recruteur-container {
    padding: 20px;
    color: white;
    background: transparent;
  
    .logo {
      width: 120px;
      margin-bottom: 10px;
      // align-items: end;
    }
  
    .header {
      text-align: center;
      margin-bottom: 30px;

      h1 {
        font-size: 28px;
        margin: 10px 0;
      }

      .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;

        .action-buttons {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }

      .ajouter-btn {
        background: linear-gradient(90deg, #ff6a00, #ee0979);
        color: white;
        margin-top: 10px;
      }
    }
  
    .offres-grid {
      display: grid;
      gap: 20px;
      padding: 10px;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  
      .offre-card {
        background: linear-gradient(to bottom, #8e017e, #5f045e);
        color: white;
        border-radius: 15px;
        transition: all 0.3s ease;

        &.nouvelle-offre {
          animation: nouvelleOffreAnimation 2s ease-in-out;
          border: 2px solid #00c853;
          box-shadow: 0 0 20px rgba(0, 200, 83, 0.3);
        }
  
        .etat {
          margin-top: 10px;
          padding: 4px 10px;
          border-radius: 12px;
          display: inline-block;
          font-size: 12px;
  
          &.Actif {
            background-color: #00c853;
          }
  
          &.Inactif {
            background-color: #d50000;
          }
        }

        .offre-details {
          margin-bottom: 15px;

          p {
            margin: 5px 0;
            font-size: 14px;

            strong {
              color: #fff;
            }
          }
        }
      }
    }

    // Animation pour les nouvelles offres
    @keyframes nouvelleOffreAnimation {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(0, 200, 83, 0);
      }
      50% {
        transform: scale(1.02);
        box-shadow: 0 0 25px rgba(0, 200, 83, 0.5);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 200, 83, 0.3);
      }
    }

    // Styles pour les états de chargement et messages
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50px;
      color: white;

      p {
        margin-top: 20px;
        font-size: 16px;
      }
    }

    .no-offers {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50px;
      color: white;
      text-align: center;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
        opacity: 0.7;
      }

      h3 {
        margin: 10px 0;
        font-size: 24px;
      }

      p {
        margin: 10px 0 30px 0;
        font-size: 16px;
        opacity: 0.8;
      }

      button {
        background: linear-gradient(90deg, #ff6a00, #ee0979);
        color: white;
      }
    }
  }

  // --------------------------------------------------------------------------
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  
    .profile-menu-container {
      position: relative;
      border: none;
    }
  
    .profile-button {
      padding: 0;
      background: none;
      border: none;
    }
  
    .profile-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: #3D0B37;
      background-color: white;
      border-radius: 50%;
      overflow: hidden;
      border: none;
    }
  
    /* Surcharger les styles Angular Material spécifiquement pour ce composant */
    ::ng-deep .mat-button,
    ::ng-deep .mat-icon-button {
      border: none;
      box-shadow: none;
    }
  }
  
.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}

// !-----------------------------------------------

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

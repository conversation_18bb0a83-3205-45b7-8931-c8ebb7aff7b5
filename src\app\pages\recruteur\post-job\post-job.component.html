<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover">
          Accueil
        </a>

        <!-- Bouton profil -->
        <div class="profile-menu-container">
          <button mat-icon-button 
                 [matMenuTriggerFor]="profileMenu" 
                 aria-label="Profil utilisateur"
                 class="profile-button">
            <div class="profile-avatar">
              <mat-icon>person</mat-icon>
            </div>
          </button>
          
          <mat-menu #profileMenu="matMenu" xPosition="before" class="profile-dropdown">
            <button mat-menu-item routerLink="/profile">
              <mat-icon>account_circle</mat-icon>
              <span>Tes infos</span>
            </button>
            <!-- <button mat-menu-item routerLink="/cv">
              <mat-icon>description</mat-icon>
              <span>Ton CV</span>
            </button> -->
            <mat-divider></mat-divider>
            <button mat-menu-item routerLink="/logout">
              <mat-icon>exit_to_app</mat-icon>
              <span>Se déconnecter</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>

<!-- ----------------------------------------------------------------------------- -->

<div class="form-container">
    <h1>{{ isEditMode ? 'Modifier une offre' : 'Ajouter une offre' }}</h1>
    
    <form [formGroup]="jobForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="title">Titre du poste</label>
        <input id="title" type="text" formControlName="title">
        <div *ngIf="jobForm.controls['title'].invalid && jobForm.controls['title'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="salary">Salaire</label>
        <input id="salary" type="text" formControlName="salary">
        <div *ngIf="jobForm.controls['salary'].invalid && jobForm.controls['salary'].touched" class="error">
            Ce champ est requis
        </div>
      </div>

        <div class="form-group">
        <label for="domaine">Type de Domaine</label>
        <select id="domaine" formControlName="domaine">
          <option value="" disabled selected>Sélectionnez un domaine</option>
          <option *ngFor="let domaine of domaine" [value]="domaine.value">{{domaine.label}}</option>
        </select>
        <div *ngIf="jobForm.controls['domaine'].invalid && jobForm.controls['domaine'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="contractType">Type de contrat</label>
        <select id="contractType" formControlName="contractType">
          <option value="" disabled selected>Sélectionnez un type de contrat</option>
          <option *ngFor="let type of contractTypes" [value]="type.value">{{type.label}}</option>
        </select>
        <div *ngIf="jobForm.controls['contractType'].invalid && jobForm.controls['contractType'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="city">Ville</label>
        <input id="city" type="text" formControlName="city">
        <div *ngIf="jobForm.controls['city'].invalid && jobForm.controls['city'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="experienceLevel">Niveau d'expérience</label>
        <select id="experienceLevel" formControlName="experienceLevel">
          <option value="" disabled selected>Sélectionnez un niveau d'expérience</option>
          <option *ngFor="let level of experienceLevels" [value]="level.value">{{level.label}}</option>
        </select>
        <div *ngIf="jobForm.controls['experienceLevel'].invalid && jobForm.controls['experienceLevel'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="skills">Compétences requises</label>
        <input id="skills" type="text" formControlName="skills">
        <div *ngIf="jobForm.controls['skills'].invalid && jobForm.controls['skills'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-group">
        <label for="description">Description du poste</label>
        <textarea id="description" rows="5" formControlName="description"></textarea>
        <div *ngIf="jobForm.controls['description'].invalid && jobForm.controls['description'].touched" class="error">
            Ce champ est requis
        </div>
      </div>
      
      <div class="form-actions">
        <button type="submit" class="submit-button" (click)="confirmerPublication()">
          {{ isEditMode ? 'Modifier l\'offre' : 'Publier l\'offre' }}
        </button>
      </div>
    </form>
  </div>

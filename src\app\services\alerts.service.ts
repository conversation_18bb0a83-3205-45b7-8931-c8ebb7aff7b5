import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../components/confirm-dialog/confirm-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class AlertsService {
  private readonly defaultDuration = 3000; // 3 secondes
  private defaultConfig = {
    duration: 3000,
    horizontalPosition: 'end' as const,
    verticalPosition: 'top' as const,
    panelClass: ['default-snackbar'] as string[]
  };

  constructor(
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  // Afficher une notification de succès
  showSuccess(message: string, duration: number = this.defaultDuration): void {
    this.snackBar.open(message, 'OK', {
      ...this.defaultConfig,
      duration,
      panelClass: [...this.defaultConfig.panelClass, 'success-snackbar'] as string[]
    });
  }

  // Afficher une notification d'erreur
  showError(message: string, duration: number = this.defaultDuration): void {
    this.snackBar.open(message, 'OK', {
      ...this.defaultConfig,
      duration,
      panelClass: [...this.defaultConfig.panelClass, 'error-snackbar'] as string[]
    });
  }

  // Afficher une notification d'information
  showInfo(message: string, duration: number = this.defaultDuration): void {
    this.snackBar.open(message, 'OK', {
      ...this.defaultConfig,
      duration,
      panelClass: [...this.defaultConfig.panelClass, 'info-snackbar'] as string[]
    });
  }

  // Afficher une notification d'avertissement
  showWarning(message: string, duration: number = this.defaultDuration): void {
    this.snackBar.open(message, 'OK', {
      ...this.defaultConfig,
      duration,
      panelClass: [...this.defaultConfig.panelClass, 'warning-snackbar'] as string[]
    });
  }

  // Afficher une boîte de dialogue de confirmation
  showConfirm(
    title: string,
    message: string,
    confirmText: string = 'Confirmer',
    cancelText: string = 'Annuler'
  ): Promise<boolean> {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: { title, message, confirmText, cancelText }
    });

    return dialogRef.afterClosed().toPromise();
  }

  // Afficher une notification persistante (nécessite une action utilisateur)
  showPersistent(
    message: string,
    actionText: string = 'OK',
    panelClass: string[] = []
  ): void {
    this.snackBar.open(message, actionText, {
      ...this.defaultConfig,
      duration: undefined, // La notification reste jusqu'à ce que l'utilisateur clique
      panelClass: [...this.defaultConfig.panelClass, ...panelClass] as string[]
    });
  }

  // Fermer toutes les notifications
  dismissAll(): void {
    this.snackBar.dismiss();
  }
}

<!-- header.component.html -->
<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover">
          Accueil
        </a>

        <!-- Bouton profil -->
        <div class="profile-menu-container">
          <button mat-icon-button 
                 [matMenuTriggerFor]="profileMenu" 
                 aria-label="Profil utilisateur"
                 class="profile-button">
            <div class="profile-avatar">
              <mat-icon>person</mat-icon>
            </div>
          </button>
          
          <mat-menu #profileMenu="matMenu" xPosition="before" class="profile-dropdown">
            <button mat-menu-item routerLink="/recruteur/interview-results">
              <mat-icon>account_circle</mat-icon>
              <span>Tes infos</span>
            </button>
            <!-- <button mat-menu-item routerLink="/cv">
              <mat-icon>description</mat-icon>
              <span>Ton CV</span>
            </button> -->
            <mat-divider></mat-divider>
            <button mat-menu-item routerLink="/logout">
              <mat-icon>exit_to_app</mat-icon>
              <span>Se déconnecter</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>

<!-- ---------------------------------------------------------- -->

<div class="results-container">
  <div class="results-card">
    <div class="header">
      <h1 class="title">Résultats candidats</h1>
      <!-- <button mat-icon-button class="menu-button">
        <mat-icon>menu</mat-icon>
      </button> -->
    </div>

    <div class="search-container">
      <mat-form-field appearance="outline" class="search-field">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput placeholder="Rechercher le nom..." [(ngModel)]="searchText">
      </mat-form-field>
    </div>

    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th >Nom</th>
            <th>Poste</th>
            <th>Score IA</th>
            <th>Décision IA</th>
            <th>Classement</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let candidate of filteredCandidates">
            <td>{{ candidate.name }}</td>
            <td>{{ candidate.position }}</td>
            <td>
              <div class="score-badge" [ngClass]="getScoreClass(candidate.score)">
                {{ candidate.score }}%
              </div>
            </td>
            <td>
              <div [ngClass]="getDecisionClass(candidate.score)">
                {{ getDecisionLabel(candidate.score) }}
              </div>
            </td>
            <td>
              <button mat-flat-button class="details-button" (click)="viewDetails(candidate)">
                Voir détails
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Détails Dialog -->
<ng-template #detailsDialog>
  <h2 mat-dialog-title>Détails du candidat</h2>
  <mat-dialog-content>
    <div *ngIf="selectedCandidate">
      <div class="candidate-header">
        <h3>{{ selectedCandidate.name }}</h3>
        <div class="score-badge-large" [ngClass]="getScoreClass(selectedCandidate.score)">
          {{ selectedCandidate.score }}%
        </div>
      </div>
      
      <div class="candidate-info">
        <p><strong>Poste:</strong> {{ selectedCandidate.position || 'Non spécifié' }}</p>
        <p><strong>Décision:</strong>
          <span [ngClass]="getDecisionClass(selectedCandidate.score)">
            {{ selectedCandidate.decision }}
          </span>
        </p>
        
        <div class="candidate-skills" *ngIf="selectedCandidate.skills">
          <h4>Compétences</h4>
          <div class="skill-bars">
            <div *ngFor="let skill of selectedCandidate.skills" class="skill-item">
              <div class="skill-header">
                <span>{{ skill.name }}</span>
                <span>{{ skill.level }}%</span>
              </div>
              <mat-progress-bar [value]="skill.level" [color]="getSkillColor(skill.level)"></mat-progress-bar>
            </div>
          </div>
        </div>
        
        <div class="candidate-notes" *ngIf="selectedCandidate.notes">
          <h4>Notes d'évaluation</h4>
          <p>{{ selectedCandidate.notes }}</p>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button mat-dialog-close>Fermer</button>
    <button mat-raised-button color="primary" *ngIf="selectedCandidate && selectedCandidate.decision !== 'Refusé'" class="btn-success text-white">
      Accepter
    </button>
  </mat-dialog-actions>
</ng-template>
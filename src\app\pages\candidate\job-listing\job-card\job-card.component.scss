// .contact-page-container {
//     max-width: 800px;
//     margin: auto;
//     padding: 2rem;
//     background: linear-gradient(135deg, #2c0f35, #4a1c5e);
//     border-radius: 12px;
//     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
  
//     h1 {
//       text-align: center;
//       color: #4a148c;
//     }
  
//     .subtitle {
//       text-align: center;
//       margin-bottom: 2rem;
//       color: #666;
//     }
  
//     .contact-info {
//       display: flex;
//       flex-wrap: wrap;
//       justify-content: space-between;
//       margin-bottom: 3rem;
  
//       .info-card {
//         flex: 1 1 45%;
//         margin: 1rem 0;
//         padding: 1rem;
//         background: #f3e5f5;
//         border-radius: 8px;
  
//         h3 {
//           margin-bottom: 0.5rem;
//           color: #6a1b9a;
//         }
  
//         a {
//           color: #4a148c;
//           text-decoration: none;
//         }
//       }
//     }
  
//     .contact-form {
//       display: flex;
//       flex-direction: column;
  
//       input,
//       textarea {
//         margin-bottom: 1rem;
//         padding: 0.75rem;
//         border: 1px solid #ccc;
//         border-radius: 8px;
//       }
  
//       button {
//         padding: 0.75rem;
//         background-color: #8e24aa;
//         color: #fff;
//         border: none;
//         border-radius: 8px;
//         font-weight: bold;
//         cursor: pointer;
  
//         &:hover {
//           background-color: #6a1b9a;
//         }
//       }
//     }
//   }
  
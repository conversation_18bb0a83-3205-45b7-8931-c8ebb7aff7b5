import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

import { LoginComponent } from './pages/authentification/login/login.component';
import { RegisterComponent } from './pages/authentification/register/register.component';
import { ForgotPasswordComponent } from './pages/authentification/forgot-password/forgot-password.component';

import { HomeComponent } from './pages/home/<USER>';
import { AProposComponent } from './components/a-propos/a-propos.component';
import { ContactComponent } from './components/contact/contact.component';
import { ResetPasswordComponent } from './pages/authentification/reset-password/reset-password.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'a-propos', component: AProposComponent },
  { path: 'reset-password', component: ResetPasswordComponent },
  
  // Routes protégées pour les candidats
  {
    path: 'candidate',
    loadChildren: () => import('./pages/candidate/candidate.module').then(m => m.CandidateModule),
    canActivate: [AuthGuard],
    data: { role: 'CANDIDAT' }
  },
  
  // Routes protégées pour les recruteurs
  {
    path: 'recruteur',
    loadChildren: () => import('./pages/recruteur/recruteur.module').then(m => m.RecruteurModule),
    canActivate: [AuthGuard],
    data: { role: 'RECRUTEUR' }
  },

  // Route par défaut
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }

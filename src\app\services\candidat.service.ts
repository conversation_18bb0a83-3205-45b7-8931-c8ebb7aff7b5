import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Candidat {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse: string;
  dateNaissance: string; // Format ISO 8601 YYYY-MM-DD
  genre: 'HOMME' | 'FEMME' | 'AUTRE';
  nationalite: string;
  photoUrl: string;
  linkedinUrl: string;
  githubUrl: string;
  competences: string[];
  experiencesProfessionnelles: ExperienceProfessionnelle[];
  formations: Formation[];
  langues: Langue[];
}

export interface ExperienceProfessionnelle {
  id: number;
  titre: string;
  entreprise: string;
  ville: string;
  dateDebut: string; // Format ISO 8601 YYYY-MM-DD
  dateFin: string; // Format ISO 8601 YYYY-MM-DD ou null si en cours
  description: string;
}

export interface Formation {
  id: number;
  titreDiplome: string;
  etablissement: string;
  ville: string;
  dateDebut: string; // Format ISO 8601 YYYY-MM-DD
  dateFin: string; // Format ISO 8601 YYYY-MM-DD
  description: string;
}

export interface Langue {
  id: number;
  nom: string;
  niveau: 'DEBUTANT' | 'INTERMEDIAIRE' | 'AVANCE' | 'BILINGUE' | 'NATIF';
}

@Injectable({
  providedIn: 'root'
})
export class CandidatService {
  private apiUrl = `${environment.apiUrl}/candidats`;

  constructor(private http: HttpClient) { }

  getCandidatProfile(id: number): Observable<Candidat> {
    return this.http.get<Candidat>(`${this.apiUrl}/${id}`);
  }

  updateCandidatProfile(id: number, candidat: Partial<Candidat>): Observable<Candidat> {
    return this.http.put<Candidat>(`${this.apiUrl}/${id}`, candidat);
  }

  // Methods for sub-entities
  addExperience(candidatId: number, experience: ExperienceProfessionnelle): Observable<ExperienceProfessionnelle> {
    return this.http.post<ExperienceProfessionnelle>(`${this.apiUrl}/${candidatId}/experiences`, experience);
  }

  updateExperience(candidatId: number, experienceId: number, experience: Partial<ExperienceProfessionnelle>): Observable<ExperienceProfessionnelle> {
    return this.http.put<ExperienceProfessionnelle>(`${this.apiUrl}/${candidatId}/experiences/${experienceId}`, experience);
  }

  deleteExperience(candidatId: number, experienceId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${candidatId}/experiences/${experienceId}`);
  }

  addFormation(candidatId: number, formation: Formation): Observable<Formation> {
    return this.http.post<Formation>(`${this.apiUrl}/${candidatId}/formations`, formation);
  }

  updateFormation(candidatId: number, formationId: number, formation: Partial<Formation>): Observable<Formation> {
    return this.http.put<Formation>(`${this.apiUrl}/${candidatId}/formations/${formationId}`, formation);
  }

  deleteFormation(candidatId: number, formationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${candidatId}/formations/${formationId}`);
  }

  addLangue(candidatId: number, langue: Langue): Observable<Langue> {
    return this.http.post<Langue>(`${this.apiUrl}/${candidatId}/langues`, langue);
  }

  updateLangue(candidatId: number, langueId: number, langue: Partial<Langue>): Observable<Langue> {
    return this.http.put<Langue>(`${this.apiUrl}/${candidatId}/langues/${langueId}`, langue);
  }

  deleteLangue(candidatId: number, langueId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${candidatId}/langues/${langueId}`);
  }

  updateCompetences(candidatId: number, competences: string[]): Observable<Candidat> {
    return this.http.put<Candidat>(`${this.apiUrl}/${candidatId}/competences`, competences);
  }
} 
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CandidateStats {
  totalApplications: number;
  interviewsCompleted: number;
  interviewsPending: number;
  averageScore: number;
  lastApplicationDate: Date;
  topSkills: string[];
  applicationStatus: {
    accepted: number;
    rejected: number;
    pending: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class StatsService {
  private apiUrl = `${environment.apiUrl}/api/stats`;

  constructor(private http: HttpClient) {}

  getCandidateStats(candidatId: number): Observable<CandidateStats> {
    return this.http.get<CandidateStats>(`${this.apiUrl}/candidat/${candidatId}`);
  }

  getRecruteurStats(recruteurId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/recruteur/${recruteurId}`);
  }

  getOffreStats(offreId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/offre/${offreId}`);
  }

  getPostulationStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/postulations`);
  }

  getEntretienStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/entretiens`);
  }

  getCompetenceStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/competences`);
  }

  getEvolutionStats(periode: 'semaine' | 'mois' | 'annee'): Observable<any> {
    return this.http.get(`${this.apiUrl}/evolution`, {
      params: { periode }
    });
  }
} 
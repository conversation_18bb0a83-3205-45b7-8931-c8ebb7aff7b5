// CV Component - Compatible avec background global
.cv-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  min-height: calc(100vh - 80px);
}

.cv-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;

  h1 {
    color: violet;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 600;
  }

  p {
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
  }
}

.cv-card {
  margin-bottom: 2rem;
  background-color: transparent;
  border-radius: 12px;

  mat-card-header {
    margin-bottom: 1rem;
  }

  mat-card-title {
    color: violet;
    font-size: 1.4rem;
    font-weight: 600;
  }
}

.upload-zone {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1.5rem;
  border: 2px dashed rgba(25, 118, 210, 0.3);
  border-radius: 12px;
  // background-color: rgba(25, 118, 210, 0.05);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.1);
  }

  button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: var(--secondary-color);
    }
  }

  .file-name {
    color: white;
    font-size: 1rem;
    font-weight: 500;
  }
}

.cv-info-display {
  padding: 1.5rem 0;

  p {
    margin: 0.75rem 0;
    color: white;
    font-size: 1rem;
    line-height: 1.6;
  }

  strong {
    color: var(--primary-color);
    font-weight: 600;
  }
}

.cv-text-preview {
  margin-top: 1.5rem;

  .extracted-text {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    background-color: rgba(248, 249, 250, 0.9);
    padding: 1.5rem;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-color);
    line-height: 1.5;
  }
}

.no-cv-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 3rem;
  color: var(--text-light);
  background-color: transparent;
  border-radius: 12px;
  margin: 2rem 0;


  mat-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    margin-bottom: 1.5rem;
    color: rgb(248, 2, 2);
  }

  p {
    max-width: 500px;
    line-height: 1.6;
    font-size: 1.1rem;
    color: white;
  }
}

mat-card-content{
    mat-hint{
    color: white;
  }
}

mat-card-actions {
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  background-color: transparent;
  border-radius: 0 0 12px 12px;

  button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.mat-primary {
      background-color: var(--primary-color);
      color: white;

      &:hover {
        background-color: var(--secondary-color);
        transform: translateY(-1px);
      }
    }

    &.mat-warn {
      background-color: var(--error-color);
      color: white;

      &:hover {
        background-color: #d32f2f;
        transform: translateY(-1px);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .cv-container {
    margin: 1rem auto;
  }

  .upload-zone {
    flex-direction: column;
    text-align: center;
  }

  mat-card-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
} 
@startuml RecrutIQ - Diagramme de Cas d'Utilisation

' Configuration du style
skinparam actorStyle awesome
skinparam packageStyle rectangle
skinparam backgroundColor white
skinparam handwritten false
skinparam actor {
    BackgroundColor white
    BorderColor #2C3E50
    FontColor #2C3E50
    FontSize 14
    FontStyle bold
}

skinparam usecase {
    BackgroundColor white
    BorderColor #3498DB
    FontColor #2C3E50
    FontSize 12
}

skinparam package {
    BackgroundColor #ECF0F1
    BorderColor #95A5A6
    FontColor #2C3E50
    FontSize 14
    FontStyle bold
}

' Définition des acteurs avec icônes modernes
actor "👤 Candidat" as Candidat
actor "👥 Recruteur" as Re<PERSON><PERSON><PERSON>ur
actor "🤖 Système IA" as IA
actor "📧 Système Email" as Email

' Définition des packages
rectangle "🔐 Authentification" {
    usecase "Inscription Standard" as UC1
    usecase "Connexion OAuth2" as UC2
    usecase "Connexion Standard" as UC3
    usecase "Sélection du Rôle" as UC4
}

rectangle "💼 Espace Candidat" {
    usecase "Télécharger CV" as UC5
    usecase "Consulter Offres" as UC6
    usecase "Postuler à une Offre" as UC7
    usecase "Participer à l'Entretien IA" as UC8
    usecase "Confirmer Disponibilité" as UC9
}

rectangle "🎯 Espace Recruteur" {
    usecase "Créer Offre d'Emploi" as UC10
    usecase "Gérer Offres" as UC11
    usecase "Consulter Candidatures" as UC12
    usecase "Évaluer Candidats" as UC13
    usecase "Accepter Candidat" as UC14
}

rectangle "🔄 Fonctionnalités Transverses" {
    usecase "Analyse CV par IA" as UC15
    usecase "Conduire Entretien IA" as UC16
    usecase "Envoi Emails" as UC17
    usecase "Gestion des Notifications" as UC18
}

' Relations Candidat
Candidat --> UC1
Candidat --> UC2
Candidat --> UC3
Candidat --> UC4
Candidat --> UC5
Candidat --> UC6
Candidat --> UC7
Candidat --> UC8
Candidat --> UC9

' Relations Recruteur
Recruteur --> UC1
Recruteur --> UC2
Recruteur --> UC3
Recruteur --> UC4
Recruteur --> UC10
Recruteur --> UC11
Recruteur --> UC12
Recruteur --> UC13
Recruteur --> UC14

' Relations Système IA
IA --> UC15
IA --> UC16

' Relations Système Email
Email --> UC17
Email --> UC18

' Inclusions
UC7 ..> UC5 : <<include>>
UC7 ..> UC15 : <<include>>
UC8 ..> UC16 : <<include>>
UC14 ..> UC17 : <<include>>
UC14 ..> UC9 : <<include>>

' Extensions
UC12 <.. UC13 : <<extend>>
UC13 <.. UC14 : <<extend>>
UC15 <.. UC17 : <<extend>>
UC16 <.. UC17 : <<extend>>

' Notes avec style amélioré
note right of UC15
  <b>Analyse du CV</b>
  --
  • Extraction avec Apache Tika
  • Évaluation par IA
  • Score de compatibilité
end note

note right of UC16
  <b>Entretien Vocal</b>
  --
  • Questions TTS
  • Réponses STT
  • Évaluation IA
end note

note right of UC17
  <b>Notifications</b>
  --
  • Emails automatiques
  • Relances programmées
  • Confirmations
end note

@enduml 
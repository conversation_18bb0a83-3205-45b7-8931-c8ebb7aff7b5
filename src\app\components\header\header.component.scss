.fixe {
  position: fixe;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}


.slide-hover {
    position: relative;
    overflow: hidden;
    transition: color 0.4s ease-in-out;
    z-index: 1;
  }
  
.slide-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background-color: currentColor;
    opacity: 0.2; // pour un effet léger
    z-index: -1;
    transition: width 0.4s ease-in-out;
  }
  
  .slide-hover:hover::before {
    width: 100%;
  }
  
  ul {
    display: flex;
    gap: 1.5rem;
    list-style: none;
    padding-top: 1.5%;
    padding-right: 0;

    li a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;
      font-size: 1.1rem;

      &:hover {
        color: violet;
      }
    }
  }
  .btn-gradient-violet {
  background: linear-gradient(135deg, #e95793, #e95793); /* Rose → Violet */
  color: white;
  border: none;
  padding: 0.5rem 1.2rem;
  font-weight: 500;
  font-size: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 0 10px rgba(185, 79, 224, 0.3);
  }
  
  .btn-gradient-violet:hover {
    background: linear-gradient(135deg, #ec5f97, #a347d8); /* لون أغمق شوية */
    box-shadow: 0 0 15px rgba(185, 79, 224, 0.5);
    transform: translateY(-2px);
  }
  .nav-links {
    list-style: none;
    display: flex;
    gap: 2rem;
    font-weight: bold;
  }
  
  .nav-links li {
    position: relative;
  }
  
  .nav-links a {
    color: white;
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s ease;
  }
  
  .nav-links a:hover {
    color: #4e064b;
  }
  
  /* Dropdown style */
  // .dropdown-menu {
  //   position: absolute;
  //   top: 100%;
  //   left: 0;
  //   background: rgba(255, 255, 255, 0.1);
  //   backdrop-filter: blur(5px);
  //   padding: 0.5rem 1rem;
  //   border-radius: 8px;
  //   display: none;
  //   flex-direction: column;
  //   z-index: 999;
  //   white-space: nowrap;
  // }
  
  // .dropdown:hover .dropdown-menu {
  //   display: flex;
  // }
  
  // .dropdown-menu li {
  //   margin: 5px 0;
  // }
  
  // .dropdown-menu a {
  //   font-size: 0.95rem;
  //   color: #fff;
  // }
  .underline-hover::after {
    content: "";
    position: absolute;
    top: 2.5rem;
    bottom: 0;
    left: 0;
    width: 0%;
    height: 2px;
    background-color: white;
    transition: width 0.3s ease;
  }
  
  .underline-hover:hover::after {
    width: 100%;
  }
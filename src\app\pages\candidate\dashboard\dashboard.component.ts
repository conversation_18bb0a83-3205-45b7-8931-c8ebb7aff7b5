import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Offre } from '../../../services/offre.service';
import { AuthService } from '../../../services/auth.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  offres: Offre[] = [];
  filteredOffres: Offre[] = [];
  searchText: string = '';
  isProfileMenuOpen = false;
  currentUserId: number | null = null;
  currentUser: any;
  isLoading = false;
  isFiltering = false;

  constructor(
    private titleService: Title,
    private router: Router,
    private authService: AuthService
  ) {
    this.currentUserId = this.authService.getCurrentUserId();
  }

  ngOnInit() {
    this.titleService.setTitle('Tableau de bord candidat');
    this.loadCurrentUser();
    this.loadStaticOffres();
  }

  private loadCurrentUser() {
    // Simuler un utilisateur connecté pour le mode statique
    this.currentUser = {
      id: 1,
      nom: 'Candidat',
      prenom: 'Test',
      email: '<EMAIL>'
    };
    this.currentUserId = 1;
  }

  private loadStaticOffres() {
    this.isLoading = true;

    // Données statiques d'offres d'emploi
    this.offres = [
      {
        id: 1,
        titre: 'Développeur Full Stack',
        description: 'Nous recherchons un développeur Full Stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant les dernières technologies web.',
        domaine: 'Informatique',
        ville: 'Rabat',
        typeContrat: 'CDI',
        salaire: '45000',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-12-31'),
        competences: ['Angular', 'Node.js', 'TypeScript', 'MongoDB'],
        recruteurId: 1,
        expanded: false
      },
      {
        id: 2,
        titre: 'Designer UX/UI',
        description: 'Rejoignez notre équipe créative en tant que Designer UX/UI. Vous concevrez des interfaces utilisateur intuitives et esthétiques pour nos applications web et mobiles.',
        domaine: 'Design',
        ville: 'Casablanca',
        typeContrat: 'CDI',
        salaire: '38000',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-11-30'),
        competences: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
        recruteurId: 2,
        expanded: false
      },
      {
        id: 3,
        titre: 'Chef de Projet Digital',
        description: 'Nous cherchons un Chef de Projet Digital pour piloter nos projets de transformation numérique. Vous coordonnerez les équipes techniques et métier.',
        domaine: 'Management',
        ville: 'Marrakech',
        typeContrat: 'CDI',
        salaire: '55000',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-12-15'),
        competences: ['Gestion de projet', 'Agile', 'Scrum', 'Leadership'],
        recruteurId: 3,
        expanded: false
      },
      {
        id: 4,
        titre: 'Développeur Mobile Flutter',
        description: 'Développeur mobile spécialisé en Flutter recherché pour créer des applications mobiles cross-platform innovantes et performantes.',
        domaine: 'Informatique',
        ville: 'Fès',
        typeContrat: 'CDD',
        salaire: '35000',
        heuresParSemaine: 40,
        dateLimite: new Date('2024-11-20'),
        competences: ['Flutter', 'Dart', 'Firebase', 'API REST'],
        recruteurId: 4,
        expanded: false
      },
      {
        id: 5,
        titre: 'Analyste Data',
        description: 'Analyste Data pour extraire des insights précieux de nos données. Vous travaillerez avec des outils modernes d\'analyse et de visualisation.',
        domaine: 'Data Science',
        ville: 'Tanger',
        typeContrat: 'Freelance',
        salaire: '40000',
        heuresParSemaine: 35,
        dateLimite: new Date('2024-12-10'),
        competences: ['Python', 'SQL', 'Power BI', 'Machine Learning'],
        recruteurId: 5,
        expanded: false
      }
    ];

    this.filteredOffres = [...this.offres];
    this.isLoading = false;
  }

  applyFilter() {
    if (!this.searchText) {
      this.filteredOffres = this.offres;
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredOffres = this.offres.filter(offre =>
      offre.titre.toLowerCase().includes(searchLower) ||
      offre.description.toLowerCase().includes(searchLower) ||
      offre.domaine.toLowerCase().includes(searchLower) ||
      offre.ville.toLowerCase().includes(searchLower) ||
      offre.typeContrat.toLowerCase().includes(searchLower) ||
      offre.competences.some((comp: string) => comp.toLowerCase().includes(searchLower))
    );
  }

  toggleProfileMenu() {
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }

  async postuler(offre: Offre) {
    if (!this.currentUserId) {
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'Vous devez être connecté pour postuler.'
      });
      return;
    }

    const { value: lettreMotivation } = await Swal.fire({
      title: 'Lettre de motivation',
      input: 'textarea',
      inputLabel: 'Veuillez rédiger votre lettre de motivation',
      inputPlaceholder: 'Écrivez votre lettre de motivation ici...',
      showCancelButton: true,
      inputValidator: (value) => {
        if (!value) {
          return 'La lettre de motivation est obligatoire';
        }
        return null;
      }
    });

    if (lettreMotivation) {
      // Simulation d'envoi de candidature en mode statique
      Swal.fire({
        icon: 'success',
        title: 'Candidature envoyée !',
        text: 'Votre candidature a été envoyée avec succès. Vous recevrez bientôt une réponse.',
        timer: 3000,
        showConfirmButton: false
      });
    }
  }

  sauvegarderOffre(offre: Offre) {
    // Simulation de sauvegarde en mode statique
    Swal.fire({
      icon: 'success',
      title: 'Offre sauvegardée !',
      text: 'L\'offre a été ajoutée à vos offres sauvegardées.',
      timer: 2000,
      showConfirmButton: false
    });
  }

  logout(): void {
    this.authService.logout();
  }
}

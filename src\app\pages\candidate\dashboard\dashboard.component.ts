import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { OffreService, Offre } from '../../../services/offre.service';
import { PostulationService } from '../../../services/postulation.service';
import { AuthService } from '../../../services/auth.service';
import { PostulationRequest } from '../../../models/postulation.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AlertsService } from '../../../services/alerts.service';
import { PostulationDialogComponent } from '../../../components/postulation-dialog/postulation-dialog.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  offres: Offre[] = [];
  filteredOffres: Offre[] = [];
  searchText: string = '';
  isProfileMenuOpen = false;
  currentUserId: number | null = null;
  currentUser: any;
  isLoading = false;
  isFiltering = false;
  filterForm: FormGroup;
  mesPostulations: any[] = [];
  postulationForm: FormGroup;

  constructor(
    private titleService: Title,
    private router: Router,
    private offreService: OffreService,
    private postulationService: PostulationService,
    private authService: AuthService,
    private alertsService: AlertsService,
    private dialog: MatDialog,
    private fb: FormBuilder
  ) {
    this.currentUserId = this.authService.getCurrentUserId();
    this.filterForm = this.fb.group({
      search: [''],
      domaine: [''],
      ville: [''],
      typeContrat: ['']
    });
    this.postulationForm = this.fb.group({
      cv: ['']
    });
  }

  ngOnInit() {
    this.titleService.setTitle('Tableau de bord candidat');
    this.loadCurrentUser();
    this.loadOffres();
    this.loadMesPostulations();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadCurrentUser() {
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe((user: any) => {
        if (user) {
          this.currentUserId = user.id;
          this.currentUser = user;
        }
      });
  }

  private loadOffres() {
    this.isLoading = true;
    this.offreService.getOffres()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (offres: Offre[]) => {
          this.offres = offres;
          this.filteredOffres = offres;
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Erreur lors du chargement des offres:', error);
          Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: 'Impossible de charger les offres d\'emploi. Veuillez réessayer plus tard.'
          });
          this.isLoading = false;
        }
      });
  }

  private loadMesPostulations(): void {
    this.postulationService.getMesPostulations().subscribe({
      next: (postulations) => {
        this.mesPostulations = postulations;
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors du chargement de vos postulations');
      }
    });
  }

  applyFilter() {
    if (!this.searchText) {
      this.filteredOffres = this.offres;
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredOffres = this.offres.filter(offre =>
      offre.titre.toLowerCase().includes(searchLower) ||
      offre.description.toLowerCase().includes(searchLower) ||
      offre.domaine.toLowerCase().includes(searchLower) ||
      offre.ville.toLowerCase().includes(searchLower) ||
      offre.typeContrat.toLowerCase().includes(searchLower) ||
      offre.competences.some((comp: string) => comp.toLowerCase().includes(searchLower))
    );
  }

  toggleProfileMenu() {
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }

  async postuler(offre: Offre) {
    if (!this.currentUserId) {
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'Vous devez être connecté pour postuler.'
      });
      return;
    }

    if (this.postulationForm.valid) {
      const cvFile = this.postulationForm.get('cv')?.value;
      const { value: lettreMotivation } = await Swal.fire({
        title: 'Lettre de motivation',
        input: 'textarea',
        inputLabel: 'Veuillez rédiger votre lettre de motivation',
        inputPlaceholder: 'Écrivez votre lettre de motivation ici...',
        showCancelButton: true,
        inputValidator: (value) => {
          if (!value) {
            return 'La lettre de motivation est obligatoire';
          }
          return null;
        }
      });

      if (lettreMotivation) {
        const formData = new FormData();
        formData.append('offreId', offre.id.toString());
        formData.append('lettreMotivation', lettreMotivation);
        formData.append('cv', cvFile);

        this.postulationService.postuler({
          offreId: offre.id,
          lettreMotivation,
          cv: cvFile
        }).subscribe({
          next: (response) => {
            Swal.fire({
              icon: 'success',
              title: 'Candidature envoyée !',
              text: 'Votre candidature a été envoyée avec succès. Vous recevrez bientôt une réponse.',
              timer: 3000,
              showConfirmButton: false
            });
          },
          error: (error) => {
            console.error('Erreur lors de la postulation:', error);
            Swal.fire({
              icon: 'error',
              title: 'Erreur',
              text: 'Une erreur est survenue lors de l\'envoi de votre candidature. Veuillez réessayer.'
            });
          }
        });
      }
    }
  }

  sauvegarderOffre(offre: Offre) {
    // TODO: Implémenter la sauvegarde des offres
    Swal.fire({
      icon: 'info',
      title: 'Fonctionnalité à venir',
      text: 'La sauvegarde des offres sera bientôt disponible.'
    });
  }

  applyFilters(): void {
    this.isFiltering = true;
    const filter = this.filterForm.value;
    this.offreService.getOffres(filter).subscribe({
      next: (offres) => {
        this.offres = offres;
        this.isFiltering = false;
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors de l\'application des filtres');
        this.isFiltering = false;
      }
    });
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadOffres();
  }

  postulerDialog(offre: Offre) {
    const dialogRef = this.dialog.open(PostulationDialogComponent, {
      width: '500px',
      data: { offreId: offre.id }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMesPostulations();
      }
    });
  }

  deconnexion(): void {
    this.authService.logout();
  }

  logout(): void {
    this.authService.logout();
  }
}

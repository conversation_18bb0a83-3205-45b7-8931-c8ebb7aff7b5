.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      padding: 0;
      // margin: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  }

.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: rgb(255, 255, 255);
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}

.social-login {
  .google-btn {
    background-color: #ea4335;
    color: white;
    padding-left: 0rem;
    &:hover {
      background-color: red;
      color: #ffffff;
      box-shadow: 0 6px 14px rgba(255, 64, 129, 0.5);
      transform: scale(1.05);
    }
  
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 64, 129, 0.4);
    }
  }
  
  
  .facebook-btn {
    background-color: blue;
    color: white;
    &:hover {
      background-color: blue;
      color: #ffffff;
      box-shadow: 0 6px 14px rgba(255, 64, 129, 0.5);
      transform: scale(1.05);
    }
  
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 64, 129, 0.4);
    }
  }
  
  .microsoft-btn {
    background-color: #2F2F2F; // Noir/gris foncé Microsoft
    color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: none;
  
    &:hover {
      background-color: #1e1e1e;
      color: #ffffff;
      box-shadow: 0 6px 14px rgba(8, 36, 59, 0.5); // bleu Microsoft
      transform: scale(1.05);
    }
  
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(0, 120, 215, 0.4);
    }
  
  }
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 24px 0;

  &:before,
  &:after {
    content: "";
    flex: 1;
    height: 1px;
    background-color: #ccc;
  }

  .divider-text {
    padding: 0 10px;
    color: #ffffff;
    font-size: 14px;
    white-space: nowrap;
    background-color: transparent;
  }
}


// --------------------------------------------------------------------------- 

.login-container {
    max-width: 400px;
    height : 620px;
    margin: 0 auto;
    padding: 10px 20px;
    border-radius: 12px;
    background: linear-gradient(145deg, #910d74 0%, #8d0f54 100%);
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    justify-content: center;
    align-items: center;
    margin-top: 5%;
    margin-bottom: 5rem;
  }
  
.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    img {
      height: 150px;
      width: auto;
      margin-right: 10px;
      display: block;
    }
  }
  
h2 {
    text-align: center;
    font-size: 1.8rem;
    margin-bottom: 30px;
    margin-top: 0.5rem;
    font-weight: 500;
    color: #fff;
  }
  
.form-group {
    margin-bottom: 15px;

    input {
      width: 100%;
      padding: 12px 15px;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 1rem;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
      
      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background-color: rgba(255, 255, 255, 0.15);
      }
      
      &.invalid {
        border-color: #ff6b6b;
      }
    }
  }
  
.password-field {
    position: relative;

    .show-password {
      position: absolute;
      right: 15px; // Même position que l'icône lock
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #000 !important; // Noir comme l'icône lock
      cursor: pointer;
      font-size: 1.2rem; // Même taille que l'icône lock
      padding: 0;
      width: auto;
      height: auto;
      z-index: 3; // Plus élevé que l'icône lock
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #333 !important;
      }

      i {
        font-size: 1.2rem;
        font-weight: bold;
        display: block;
      }
    }
  }
  
.login-button {
  background-color: rgba(255, 64, 129, 0.2);
  color: white;
  width: 100%;
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 18px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(255, 64, 129, 0.3);

  &:hover {
    background-color: #ff4081;
    color: #ffffff;
    box-shadow: 0 6px 14px rgba(255, 64, 129, 0.5);
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 64, 129, 0.4);
  }
  }
  
.forgot-password {
    text-align: center;
    margin-top: 15px;
    
    a {
      // color: rgba(255, 255, 255, 0.7);
      color: #2acbe0;
      text-decoration: none;
      font-size: 0.9rem;
      cursor: pointer;
      
      &:hover {
        color: #ff4081;
        text-decoration: underline;
      }
    }
  }
  
.register-link {
    text-align: center;
    margin-top: 20px;
    font-size: 0.9rem;
    
    span {
      color: rgba(255, 255, 255, 0.7);
    }
    
    a {
      color: #2acbe0;
      text-decoration: none;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
        color: #ff4081;
      }
    }
  }

  // -----------------------------------------------------
  .input-icon-wrapper {
    position: relative;

    // Icônes fixes à droite en noir (person pour les deux champs)
    .icon-right {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #000 !important; // Noir fixe
      font-size: 1.2rem;
      font-weight: bold;
      pointer-events: none;
      z-index: 2;
      opacity: 1 !important; // Toujours visible
    }
  }

  // Supprimé - conflit avec la définition principale de .password-field
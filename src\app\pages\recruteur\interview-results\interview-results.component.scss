  // --------------------------------------------------------------------------
  :host {
    /* Cibler uniquement le composant header */
    header {
      background-color: transparent;
      padding-bottom: 0;
      margin-bottom: 0;
      border: none;
      box-shadow: none;
    }
  
    .navbar {
      padding: 0rem 1rem;
      border: none;
    }
  
    .logo-container {
      display: flex;
      align-items: center;
      border: none;
    }
  
    .navbar-brand, 
    .nav-item, 
    .profile-button {
      border: none;
    }
  
    .logo-image {
      max-height: 100px;
      max-width: auto;
    }
  
    .nav-item {
      color: white;
      font-weight: 500;
      &:hover {
        color: violet;
      }
    }
  
    .profile-menu-container {
      position: relative;
      border: none;
    }
  
    .profile-button {
      padding: 0;
      background: none;
      border: none;
    }
  
    .profile-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: #3D0B37;
      background-color: white;
      border-radius: 50%;
      overflow: hidden;
      border: none;
    }
  
    /* Surcharger les styles Angular Material spécifiquement pour ce composant */
    ::ng-deep .mat-button,
    ::ng-deep .mat-icon-button {
      border: none;
      box-shadow: none;
    }
  }
  
.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// !-----------------------------------------------



.results-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  padding: 20px;
  font-family: 'Roboto', sans-serif;
}

.results-card {
  width: 100%;
  max-width: 1200px;
  background-color: #3f0e45;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  color: #ff4081;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

// .menu-button {
//   color: #ff4081;
// }

.search-container {
  margin-bottom: 20px;
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-end;
}

.search-field {
  width: 30%;
  background-color: #3f0e45;
  border-radius: 8px;
  color: white;
}

::ng-deep .search-field .mat-form-field-outline {
  color: #ff4081;
}

::ng-deep .search-field .mat-form-field-flex {
  background-color: #3f0e45;
}

::ng-deep .search-field input {
  color: white;
}

::ng-deep .search-field mat-icon {
  color: #ff4081;
}

::ng-deep .search-field .mat-form-field-label {
  color: rgba(255, 255, 255, 0.6);
}

.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  color: white;
  border-collapse: separate;
  border-spacing: 0 10px;
}

th {
  color: #ff4081;
  font-size: 1.2rem;
  font-weight: 500;
  padding: 10px 15px;
  text-align: left;
  background-color: transparent;
}

td {
  padding: 15px;
  font-size: 1.1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: transparent;
  color: white;
}

.score-badge {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  text-align: center;
  min-width: 70px;
}

.score-badge-large {
  display: inline-block;
  padding: 8px 20px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  text-align: center;
  min-width: 80px;
  font-size: 1.2rem;
}

.score-high {
  background-color: #1b5e20;
}

.score-medium {
  background-color: #b71c1c;
}

.score-average {
  background-color: #e65100;
}

.score-low {
  background-color: #b71c1c;
}

.decision-admitted {
  color: #1b5e20;
}

.decision-interview {
  color: #ffc107;
}

.decision-rejected {
  color: #b71c1c;
}

.details-button {
  background-color: rgba(255, 64, 129, 0.2);
  color: #ff4081;
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 18px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(255, 64, 129, 0.3);

  &:hover {
    background-color: #ff4081;
    color: #ffffff;
    box-shadow: 0 6px 14px rgba(255, 64, 129, 0.5);
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 64, 129, 0.4);
  }
}


// .details-button {
//   background-color: rgba(255, 64, 129, 0.2);
//   color: #ff4081;
//   border-radius: 8px;
//   font-weight: 500;
// }

// candidates-details.component.scss pour Angular Material 14

// Style pour le dialog container - IMPORTANT: ceci applique le background
::ng-deep .custom-dialog .mat-dialog-container {
  background: linear-gradient(135deg, #2c0f35, #4a1c5e) !important;
  color: white !important;
  border-radius: 20px !important;
  padding: 32px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

// Styles pour les éléments internes du dialog
.mat-dialog-title {
  color: #ff4081;
  font-size: 2rem;
  margin-bottom: 20px;
  font-weight: bold;
  text-align: center;
}

.mat-dialog-content {
  max-height: 70vh;
  overflow: auto;
  padding-right: 8px;
}

.mat-dialog-actions {
  margin-top: 24px;
  gap: 12px;
  
  button {
    font-weight: 600;
    padding: 8px 18px;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 64, 129, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 64, 129, 0.5);
    }
  }
  
  button[mat-button] {
    background-color: transparent;
    color: #ff4081;
    border: 1px solid #ff4081;
    
    &:hover {
      background-color: rgba(255, 64, 129, 0.1);
    }
  }
  
  button[mat-raised-button] {
    background: linear-gradient(to right, #ff4081, #ff80ab);
    color: white;
    
    &:hover {
      background: linear-gradient(to right, #ff2f70, #ff66a1);
    }
  }
}

.candidate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h3 {
    font-size: 1.5rem;
    margin: 0;
  }
}

.score-badge-large {
  padding: 8px 16px;
  border-radius: 30px;
  font-weight: bold;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-high {
  background-color: #4CAF50;
  color: white;
}

.score-medium {
  background-color: #FFC107;
  color: #333;
}

.score-low {
  background-color: #F44336;
  color: white;
}

.candidate-info {
  font-size: 1.1rem;
  
  p {
    margin: 12px 0;
    line-height: 1.6;
  }
  
  h4 {
    color: #ff80ab;
    font-size: 1.4rem;
    margin: 24px 0 12px;
    font-weight: 600;
  }
}

.status-accepted {
  color: #4CAF50;
  font-weight: bold;
}

.status-pending {
  color: #FFC107;
  font-weight: bold;
}

.status-rejected {
  color: #F44336;
  font-weight: bold;
}

.candidate-notes {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-left: 4px solid #ff4081;
  border-radius: 12px;
  font-style: italic;
  box-shadow: inset 0 0 8px rgba(255, 64, 129, 0.2);
  margin-top: 20px;
}

.skill-bars {
  .skill-item {
    margin-bottom: 18px;
  }
  
  .skill-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 0.95rem;
    font-weight: 500;
  }
  
  mat-progress-bar {
    border-radius: 8px;
    height: 10px;
    
    ::ng-deep .mat-progress-bar-fill::after {
      border-radius: 8px;
    }
  }
}

.entretien-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.demarrage {
  text-align: center;
  margin: 50px 0;
  
  button {
    padding: 15px 30px;
    font-size: 1.2em;
    background-color: #3D0B37;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    
    &:hover {
      background-color: #4a1c5e;
    }
  }
}

.question-container {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .question {
    margin-bottom: 30px;
    
    h3 {
      color: #3D0B37;
      font-size: 1.4em;
      line-height: 1.6;
    }
  }
  
  .reponse {
    margin: 20px 0;
    
    button {
      padding: 12px 24px;
      margin: 0 10px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      
      &:first-child {
        background-color: #4CAF50;
        color: white;
      }
      
      &:last-child {
        background-color: #f44336;
        color: white;
      }
    }
  }
  
  .navigation {
    margin-top: 30px;
    text-align: center;
    
    button {
      padding: 12px 24px;
      background-color: #3D0B37;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      
      &:hover {
        background-color: #4a1c5e;
      }
    }
  }
}

.cv-upload-container {
  padding: 20px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  text-align: center;

  input[type="file"] {
    display: none;
  }

  button {
    margin-top: 10px;
    padding: 10px 20px;
    background-color: #3D0B37;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}

.audio-recorder {
  .recording {
    background-color: #f44336;
    animation: pulse 1.5s infinite;
  }

  .audio-player {
    margin-top: 20px;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-center {
  position: relative;

  .notification-list {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 4px;
    z-index: 1000;
  }

  .notification-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
}

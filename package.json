{"name": "pfe-interface", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "^16.1.0", "@angular/compiler": "^16.1.0", "@angular/core": "^16.1.0", "@angular/forms": "^16.1.0", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.1.0", "@angular/platform-browser-dynamic": "^16.1.0", "@angular/router": "^16.1.0", "@auth0/angular-jwt": "^5.1.2", "@fortawesome/fontawesome-free": "^6.7.2", "bootstrap": "^5.3.5", "bootstrap-icons": "^1.11.3", "rxjs": "~7.8.0", "sweetalert2": "^11.22.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.1.8", "@angular/cli": "~16.1.8", "@angular/compiler-cli": "^16.1.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}